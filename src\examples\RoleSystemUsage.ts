/**
 * Example usage of the new Role-based Access Control (RBAC) system
 * This file demonstrates how to use the new many-to-many role system
 */

import { User, Role, UserRole } from "../models";

export class RoleSystemExamples {
    // Example: Create roles
    static async createRoles() {
        const roles = [
            { name: "admin", description: "Full system access" },
            { name: "manager", description: "Manage bookings and services" },
            { name: "employee", description: "Handle assigned bookings" },
            { name: "user", description: "Regular customer access" },
            {
                name: "vip",
                description: "VIP customer with special privileges",
            },
        ];

        for (const roleData of roles) {
            await Role.findOrCreateRecord({
                where: { name: roleData.name },
                defaults: roleData,
            });
        }

        console.log("Roles created/verified");
    }

    // Example: Create user and assign multiple roles
    static async createUserWithRoles() {
        // Create a user
        const user = await User.createRecord({
            firstName: "Jane",
            lastName: "Manager",
            email: "<EMAIL>",
            password: "manager123",
            phone: "5551234567",
        });

        // Assign multiple roles
        await user.assignRole("manager");
        await user.assignRole("employee");

        console.log("User created with manager and employee roles");
        return user;
    }

    // Example: Check user permissions
    static async checkUserPermissions(userId: number) {
        const user = await User.findById(userId);
        if (!user) {
            throw new Error("User not found");
        }

        // Get all user roles
        const roles = await user.getRoles();
        console.log(`User ${user.email} has roles:`, roles);

        // Check specific roles
        const isAdmin = await user.hasRole("admin");
        const isManager = await user.hasRole("manager");
        const canManageBookings = await user.hasAnyRole(["admin", "manager"]);

        console.log({
            isAdmin,
            isManager,
            canManageBookings,
        });

        return { roles, isAdmin, isManager, canManageBookings };
    }

    // Example: Assign role with audit trail
    static async assignRoleWithAudit(
        userId: number,
        roleName: string,
        assignedByUserId: number
    ) {
        const user = await User.findById(userId);
        if (!user) {
            throw new Error("User not found");
        }

        // Check if user already has the role
        const hasRole = await user.hasRole(roleName);
        if (hasRole) {
            console.log(`User already has role: ${roleName}`);
            return;
        }

        // Assign role with audit info
        await user.assignRole(roleName, assignedByUserId);

        console.log(
            `Role ${roleName} assigned to user ${user.email} by user ${assignedByUserId}`
        );
    }

    // Example: Remove role from user
    static async removeUserRole(userId: number, roleName: string) {
        const user = await User.findById(userId);
        if (!user) {
            throw new Error("User not found");
        }

        const hasRole = await user.hasRole(roleName);
        if (!hasRole) {
            console.log(`User doesn't have role: ${roleName}`);
            return;
        }

        await user.removeRole(roleName);
        console.log(`Role ${roleName} removed from user ${user.email}`);
    }

    // Example: Get all users with a specific role
    static async getUsersWithRole(roleName: string) {
        const role = await Role.findByName(roleName);
        if (!role) {
            throw new Error(`Role ${roleName} not found`);
        }

        const userRoles = await UserRole.getUsersWithRole(role.id);
        const users = userRoles.map((ur: any) => ({
            id: ur.User.id,
            name: `${ur.User.firstName} ${ur.User.lastName}`,
            email: ur.User.email,
            assignedAt: ur.assignedAt,
        }));

        console.log(`Users with role ${roleName}:`, users);
        return users;
    }

    // Example: Bulk role assignment
    static async bulkAssignRoles(userId: number, roleNames: string[]) {
        const user = await User.findById(userId);
        if (!user) {
            throw new Error("User not found");
        }

        console.log(
            `Assigning roles ${roleNames.join(", ")} to user ${user.email}`
        );

        for (const roleName of roleNames) {
            try {
                await user.assignRole(roleName);
                console.log(`✅ Assigned role: ${roleName}`);
            } catch (error) {
                console.log(`❌ Failed to assign role ${roleName}:`, error);
            }
        }

        const finalRoles = await user.getRoles();
        console.log(`User now has roles:`, finalRoles);
        return finalRoles;
    }

    // Example: Role-based query filtering
    static async getBookingsBasedOnUserRole(userId: number) {
        const user = await User.findById(userId);
        if (!user) {
            throw new Error("User not found");
        }

        const roles = await user.getRoles();
        const { Booking, Service } = require("../models");

        let bookings;

        if (roles.includes("admin")) {
            // Admin can see all bookings
            bookings = await Booking.findAll({
                include: [User, Service],
                order: [["createdAt", "DESC"]],
            });
            console.log("Admin view: All bookings");
        } else if (roles.includes("manager")) {
            // Manager can see all bookings but with different data
            bookings = await Booking.findAll({
                include: [
                    {
                        model: User,
                        attributes: ["firstName", "lastName", "email"],
                    },
                    { model: Service, attributes: ["name", "price"] },
                ],
                order: [["createdDate", "DESC"]],
            });
            console.log("Manager view: All bookings with limited user data");
        } else {
            // Regular users can only see their own bookings
            bookings = await Booking.findAll({
                where: { userId: user.id },
                include: [Service],
                order: [["createdAt", "DESC"]],
            });
            console.log("User view: Own bookings only");
        }

        return bookings;
    }

    // Example: Check if user can perform action
    static async canUserPerformAction(
        userId: number,
        action: string
    ): Promise<boolean> {
        const user = await User.findById(userId);
        if (!user) {
            return false;
        }

        const roles = await user.getRoles();

        const permissions = {
            create_booking: ["admin", "manager", "employee", "user", "vip"],
            view_all_bookings: ["admin", "manager"],
            update_booking_status: ["admin", "manager"],
            delete_booking: ["admin"],
            manage_users: ["admin"],
            manage_roles: ["admin"],
            view_reports: ["admin", "manager"],
            manage_services: ["admin", "manager"],
        };

        const requiredRoles = permissions[action as keyof typeof permissions];
        if (!requiredRoles) {
            return false;
        }

        return roles.some((role) => requiredRoles.includes(role));
    }

    // Example: Get user permissions summary
    static async getUserPermissionsSummary(userId: number) {
        const user = await User.findById(userId);
        if (!user) {
            throw new Error("User not found");
        }

        const roles = await user.getRoles();

        const actions = [
            "create_booking",
            "view_all_bookings",
            "update_booking_status",
            "delete_booking",
            "manage_users",
            "manage_roles",
            "view_reports",
            "manage_services",
        ];

        const permissions: Record<string, boolean> = {};

        for (const action of actions) {
            permissions[action] = await this.canUserPerformAction(
                userId,
                action
            );
        }

        return {
            user: {
                id: user.id,
                email: user.email,
                name: `${user.firstName} ${user.lastName}`,
            },
            roles,
            permissions,
        };
    }

    // Example: Role hierarchy check
    static async hasHigherOrEqualRole(
        userId: number,
        targetRole: string
    ): Promise<boolean> {
        const user = await User.findById(userId);
        if (!user) {
            return false;
        }

        const roles = await user.getRoles();

        // Define role hierarchy (higher number = more permissions)
        const roleHierarchy = {
            user: 1,
            vip: 2,
            employee: 3,
            manager: 4,
            admin: 5,
        };

        const userHighestRole = Math.max(
            ...roles.map(
                (role) => roleHierarchy[role as keyof typeof roleHierarchy] || 0
            )
        );

        const targetRoleLevel =
            roleHierarchy[targetRole as keyof typeof roleHierarchy] || 0;

        return userHighestRole >= targetRoleLevel;
    }
}
