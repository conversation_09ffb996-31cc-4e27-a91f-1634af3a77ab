import express from "express";
import * as roleController from "../controllers/roleController";
import { authenticate, authorize, authorizeAll } from "../middleware/auth";

const router = express.Router();

// Get all roles (admin only)
router.get("/", authenticate, authorize(["admin"]), roleController.getAllRoles);

// Create new role (admin only)
router.post("/", authenticate, authorize(["admin"]), roleController.createRole);

// Assign role to user (admin only)
router.post(
    "/assign",
    authenticate,
    authorize(["admin"]),
    roleController.assignRoleToUser
);

// Remove role from user (admin only)
router.post(
    "/remove",
    authenticate,
    authorize(["admin"]),
    roleController.removeRoleFromUser
);

// Bulk assign roles to user (admin only)
router.post(
    "/bulk-assign",
    authenticate,
    authorize(["admin"]),
    roleController.bulkAssignRoles
);

// Get user's roles (admin or the user themselves)
router.get(
    "/user/:userId",
    authenticate,
    (req, res, next) => {
        // Allow admin or the user themselves to view roles
        if (
            req.user?.roles.includes("admin") ||
            req.user?.id === parseInt(req.params.userId)
        ) {
            next();
        } else {
            res.status(403).json({ message: "Access denied" });
        }
    },
    roleController.getUserRoles
);

// Get users with specific role (admin or manager)
router.get(
    "/users/:roleName",
    authenticate,
    authorize(["admin", "manager"]),
    roleController.getUsersWithRole
);

export default router;
