import express from "express";
import * as teamController from "../controllers/teamController";
import { authenticate, authorize } from "../middleware/auth";

const router = express.Router();

// Get all teams (authenticated users can view)
router.get("/", authenticate, authorize(["admin", "manager"]), teamController.getAllTeams);

// Get team by ID (authenticated users can view)
router.get("/:id", authenticate, authorize(["admin", "manager"]), teamController.getTeamById);

// Create new team (admin and managers only)
router.post(
    "/",
    authenticate,
    authorize(["admin", "manager"]),
    teamController.createTeam
);

// Update team (admin, managers, and team leaders)
router.put(
    "/:id",
    authenticate,
    authorize(["admin", "manager"]),
    (req, _res, next) => {
        // Allow admin, manager, or team leader to update
        if (
            req.user?.roles.includes("admin") ||
            req.user?.roles.includes("manager")
        ) {
            next();
        } else {
            // Check if user is team leader (this will be validated in controller)
            next();
        }
    },
    teamController.updateTeam
);

// Check if team can be deleted (admin and managers)
router.get(
    "/:id/can-delete",
    authenticate,
    authorize(["admin", "manager"]),
    teamController.checkTeamDeletion
);

// Delete team (admin only)
router.delete(
    "/:id",
    authenticate,
    authorize(["admin"]),
    teamController.deleteTeam
);

// Add user to team (admin, managers, and team leaders)
router.post(
    "/add-member",
    authenticate,
    authorize(["admin", "manager"]),
    teamController.addUserToTeam
);

// Remove user from team (admin, managers, and team leaders)
router.post(
    "/remove-member",
    authenticate,
    authorize(["admin", "manager"]),
    teamController.removeUserFromTeam
);

// Get team members (authenticated users can view)
router.get("/:id/members", authenticate, teamController.getTeamMembers);

// Get team's future bookings (admin and managers only - for reassignment purposes)
router.get(
    "/:id/future-bookings",
    authenticate,
    authorize(["admin", "manager"]),
    teamController.getTeamFutureBookings
);

export default router;
