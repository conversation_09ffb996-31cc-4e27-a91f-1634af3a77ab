import { Request, Response } from "express";
import { User, Role, UserRole } from "../models";

// Get all roles
export const getAllRoles = async (req: Request, res: Response) => {
    try {
        const roles = await Role.findAll({
            attributes: ["id", "name", "description"],
            order: [["name", "ASC"]],
        });

        res.status(200).json({
            message: "Roles retrieved successfully",
            roles,
        });
    } catch (error) {
        console.error("Get roles error:", error);
        res.status(500).json({ message: "Error retrieving roles" });
    }
};

// Create a new role
export const createRole = async (req: Request, res: Response) => {
    try {
        const { name, description } = req.body;

        if (!name) {
            return res.status(400).json({ message: "Role name is required" });
        }

        // Check if role already exists
        const existingRole = await Role.findByName(name);
        if (existingRole) {
            return res.status(400).json({ message: "Role already exists" });
        }

        const role = await Role.createRecord({
            name: name.toLowerCase(),
            description,
        });

        res.status(201).json({
            message: "Role created successfully",
            role: {
                id: role.id,
                name: role.name,
                description: role.description,
            },
        });
    } catch (error) {
        console.error("Create role error:", error);
        res.status(500).json({ message: "Error creating role" });
    }
};

// Assign role to user
export const assignRoleToUser = async (req: Request, res: Response) => {
    try {
        const { userId, roleName } = req.body;

        if (!userId || !roleName) {
            return res.status(400).json({
                message: "User ID and role name are required",
            });
        }

        const [user, role] = await Promise.all([
            User.findById(userId),
            Role.findByName(roleName)
        ]);

        if (!user) {
            return res.status(404).json({ message: "User not found" });
        }

        if (!role) {
            return res.status(404).json({ message: "Role not found" });
        }

        // Check if user already has this role
        const hasRole = await user.hasRole(roleName);
        if (hasRole) {
            return res.status(400).json({
                message: "User already has this role",
            });
        }

        // Assign role
        await user.assignRole(roleName, req.user?.id);

        res.status(200).json({
            message: "Role assigned successfully",
            user: {
                id: user.id,
                email: user.email,
            },
        });
    } catch (error) {
        console.error("Assign role error:", error);
        res.status(500).json({ message: "Error assigning role" });
    }
};

// Remove role from user
export const removeRoleFromUser = async (req: Request, res: Response) => {
    try {
        const { userId, roleName } = req.body;

        if (!userId || !roleName) {
            return res.status(400).json({
                message: "User ID and role name are required",
            });
        }

        // Check if user exists
        const user = await User.findById(userId);
        if (!user) {
            return res.status(404).json({ message: "User not found" });
        }

        // Check if user has this role
        const hasRole = await user.hasRole(roleName);
        if (!hasRole) {
            return res.status(400).json({
                message: "User does not have this role",
            });
        }

        // Remove role
        await user.removeRole(roleName);

        res.status(200).json({
            message: "Role removed successfully",
            user: {
                id: user.id,
                email: user.email,
            },
        });
    } catch (error) {
        console.error("Remove role error:", error);
        res.status(500).json({ message: "Error removing role" });
    }
};

// Get user's roles
export const getUserRoles = async (req: Request, res: Response) => {
    try {
        const { userId } = req.params;

        // Check if user exists
        const user = await User.findById(parseInt(userId));
        if (!user) {
            return res.status(404).json({ message: "User not found" });
        }

        // Get user roles
        const roles = await user.getRoles();

        res.status(200).json({
            message: "User roles retrieved successfully",
            user: {
                id: user.id,
                email: user.email,
                firstName: user.firstName,
                lastName: user.lastName,
                roles,
            },
        });
    } catch (error) {
        console.error("Get user roles error:", error);
        res.status(500).json({ message: "Error retrieving user roles" });
    }
};

// Get users with specific role
export const getUsersWithRole = async (req: Request, res: Response) => {
    try {
        const { roleName } = req.params;

        // Check if role exists
        const role = await Role.findByName(roleName);
        if (!role) {
            return res.status(404).json({ message: "Role not found" });
        }

        // Get users with this role
        const userRoles = await UserRole.getUsersWithRole(role.id);

        // Extract user information
        const users = userRoles.map((ur: any) => ({
            id: ur.User.id,
            firstName: ur.User.firstName,
            lastName: ur.User.lastName,
            email: ur.User.email,
            assignedAt: ur.assignedAt,
        }));

        res.status(200).json({
            message: `Users with role '${roleName}' retrieved successfully`,
            role: {
                id: role.id,
                name: role.name,
                description: role.description,
            },
            users,
        });
    } catch (error) {
        console.error("Get users with role error:", error);
        res.status(500).json({ message: "Error retrieving users with role" });
    }
};

// Bulk assign roles to user
export const bulkAssignRoles = async (req: Request, res: Response) => {
    try {
        const { userId, roleNames } = req.body;

        if (!userId || !roleNames || !Array.isArray(roleNames)) {
            return res.status(400).json({
                message: "User ID and array of role names are required",
            });
        }

        // Check if user exists
        const user = await User.findById(userId);
        if (!user) {
            return res.status(404).json({ message: "User not found" });
        }

        // Assign all roles
        for (const roleName of roleNames) {
            try {
                await user.assignRole(roleName, req.user?.id);
            } catch (error) {
                console.warn(`Failed to assign role ${roleName}:`, error);
            }
        }

        // Get updated user roles
        const userRoles = await user.getRoles();

        res.status(200).json({
            message: "Roles assigned successfully",
            user: {
                id: user.id,
                email: user.email,
                roles: userRoles,
            },
        });
    } catch (error) {
        console.error("Bulk assign roles error:", error);
        res.status(500).json({ message: "Error assigning roles" });
    }
};
