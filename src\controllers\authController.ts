import { Request, Response } from "express";
import jwt from "jsonwebtoken";
import { User } from "../models";
import jwtConfig from "../config/jwt";

export const register = async (req: Request, res: Response) => {
    try {
        const { firstName, lastName, email, password, phone, roles } = req.body;

        // Check if user already exists
        const existingUser = await User.findOneRecord({ where: { email } });
        if (existingUser) {
            return res
                .status(400)
                .json({ message: "User already exists with this email" });
        }

        // Create new user
        const user = await User.createRecord({
            firstName,
            lastName,
            email,
            password,
            phone,
        });

        // Assign default "user" role if no roles specified
        const rolesToAssign = roles && roles.length > 0 ? roles : ["user"];

        for (const roleName of rolesToAssign) {
            await user.assignRole(roleName);
        }

        // Get user's roles for JWT token
        const userRoles = await user.getRoles();

        // Generate JWT token
        const token = jwt.sign(
            { id: user.id, email: user.email, roles: userRoles },
            jwtConfig.secret,
            { expiresIn: jwtConfig.expiresIn } as jwt.SignOptions
        );

        res.status(201).json({
            message: "User registered successfully",
            token,
            user: {
                id: user.id,
                firstName: user.firstName,
                lastName: user.lastName,
                email: user.email,
                phone: user.phone,
                roles: userRoles,
            },
        });
    } catch (error) {
        console.error("Registration error:", error);
        res.status(500).json({ message: "Error registering user" });
    }
};

export const login = async (req: Request, res: Response) => {
    try {
        const { email, password } = req.body;

        // Find user by email
        const user = await User.findOneRecord({ where: { email } });
        if (!user) {
            return res
                .status(401)
                .json({ message: "Invalid email or password" });
        }

        // Validate password
        const isPasswordValid = await user.validatePassword(password);
        if (!isPasswordValid) {
            return res
                .status(401)
                .json({ message: "Invalid email or password" });
        }

        // Get user's roles
        const userRoles = await user.getRoles();

        // Generate JWT token
        const token = jwt.sign(
            { id: user.id, email: user.email, roles: userRoles },
            jwtConfig.secret,
            { expiresIn: jwtConfig.expiresIn } as jwt.SignOptions
        );

        res.status(200).json({
            message: "Login successful",
            token,
            user: {
                id: user.id,
                firstName: user.firstName,
                lastName: user.lastName,
                email: user.email,
                phone: user.phone,
                roles: userRoles,
            },
        });
    } catch (error) {
        console.error("Login error:", error);
        res.status(500).json({ message: "Error during login" });
    }
};
