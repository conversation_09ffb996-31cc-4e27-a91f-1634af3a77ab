/**
 * Example usage of Stripe integration
 * This file demonstrates how to use the Stripe functionality
 */

import { User } from "../models";
import stripeService from "../services/stripeService";

export class StripeExamples {

    // Example: Create a user and automatically get Stripe customer
    static async createUserWithStripeCustomer() {
        try {
            console.log('Creating user with automatic Stripe customer...');
            
            const user = await User.createRecord({
                firstName: 'John',
                lastName: 'Doe',
                email: '<EMAIL>',
                password: 'securepassword123',
                phone: '+1234567890'
            });

            console.log(`User created with ID: ${user.id}`);
            
            // Wait for the afterCreate hook to complete
            await new Promise(resolve => setTimeout(resolve, 1000));
            
            // Reload to get the updated stripeCustomerId
            await user.reload();
            
            if (user.stripeCustomerId) {
                console.log(`Stripe customer automatically created: ${user.stripeCustomerId}`);
                return user;
            } else {
                console.log('Stripe customer creation may have failed, trying manual creation...');
                const customerId = await user.ensureStripeCustomer();
                console.log(`Stripe customer manually created: ${customerId}`);
                return user;
            }
        } catch (error) {
            console.error('Error creating user with Stripe customer:', error);
            throw error;
        }
    }

    // Example: Get customer information
    static async getCustomerInfo(user: User) {
        try {
            console.log('Getting Stripe customer information...');
            
            const stripeCustomer = await user.getStripeCustomer();
            
            console.log('Customer Info:', {
                id: stripeCustomer.id,
                email: stripeCustomer.email,
                name: stripeCustomer.name,
                phone: stripeCustomer.phone,
                created: new Date(stripeCustomer.created * 1000).toISOString()
            });
            
            return stripeCustomer;
        } catch (error) {
            console.error('Error getting customer info:', error);
            throw error;
        }
    }

    // Example: Create payment intent for a booking
    static async createPaymentForBooking(user: User, bookingAmount: number, bookingId?: number) {
        try {
            console.log(`Creating payment intent for $${bookingAmount}...`);
            
            // Ensure user has Stripe customer
            const customerId = await user.ensureStripeCustomer();
            
            // Create payment intent
            const paymentIntent = await stripeService.createPaymentIntent(
                Math.round(bookingAmount * 100), // Convert to cents
                'usd',
                customerId,
                {
                    userId: user.id.toString(),
                    bookingId: bookingId?.toString() || '',
                    description: 'Mobile Car Wash Service'
                }
            );
            
            console.log('Payment Intent Created:', {
                id: paymentIntent.id,
                amount: paymentIntent.amount / 100, // Convert back to dollars
                currency: paymentIntent.currency,
                status: paymentIntent.status,
                client_secret: paymentIntent.client_secret
            });
            
            return paymentIntent;
        } catch (error) {
            console.error('Error creating payment intent:', error);
            throw error;
        }
    }

    // Example: Get payment methods for a customer
    static async getPaymentMethods(user: User) {
        try {
            console.log('Getting payment methods...');
            
            const paymentMethods = await user.getPaymentMethods();
            
            console.log(`Found ${paymentMethods.length} payment methods:`);
            paymentMethods.forEach((pm, index) => {
                console.log(`  ${index + 1}. ${pm.type} - ${pm.card?.brand} ending in ${pm.card?.last4}`);
            });
            
            return paymentMethods;
        } catch (error) {
            console.error('Error getting payment methods:', error);
            throw error;
        }
    }

    // Example: Complete workflow for processing a booking payment
    static async processBookingPayment(
        userEmail: string,
        bookingAmount: number,
        bookingId: number
    ) {
        try {
            console.log('\n=== Processing Booking Payment ===');
            
            // 1. Find user
            const user = await User.findOne({ where: { email: userEmail } });
            if (!user) {
                throw new Error('User not found');
            }
            
            console.log(`Processing payment for user: ${user.firstName} ${user.lastName}`);
            
            // 2. Ensure Stripe customer exists
            const customerId = await user.ensureStripeCustomer();
            console.log(`Stripe customer: ${customerId}`);
            
            // 3. Create payment intent
            const paymentIntent = await this.createPaymentForBooking(user, bookingAmount, bookingId);
            
            // 4. Return client secret for frontend to complete payment
            return {
                success: true,
                clientSecret: paymentIntent.client_secret,
                paymentIntentId: paymentIntent.id,
                amount: bookingAmount,
                customerId: customerId
            };
            
        } catch (error) {
            console.error('Error processing booking payment:', error);
            return {
                success: false,
                error: error instanceof Error ? error.message : 'Unknown error'
            };
        }
    }

    // Example: Update customer information in Stripe
    static async updateCustomerInfo(user: User, updates: { name?: string; phone?: string; email?: string }) {
        try {
            console.log('Updating Stripe customer information...');
            
            if (!user.stripeCustomerId) {
                throw new Error('User does not have a Stripe customer');
            }
            
            const updatedCustomer = await stripeService.updateCustomer(
                user.stripeCustomerId,
                updates
            );
            
            console.log('Customer updated successfully:', {
                id: updatedCustomer.id,
                email: updatedCustomer.email,
                name: updatedCustomer.name,
                phone: updatedCustomer.phone
            });
            
            return updatedCustomer;
        } catch (error) {
            console.error('Error updating customer:', error);
            throw error;
        }
    }
}

// Example usage function
export async function demonstrateStripeIntegration() {
    try {
        console.log('🚀 Demonstrating Stripe Integration\n');
        
        // Create user with Stripe customer
        const user = await StripeExamples.createUserWithStripeCustomer();
        
        // Get customer info
        await StripeExamples.getCustomerInfo(user);
        
        // Get payment methods (will be empty for new customer)
        await StripeExamples.getPaymentMethods(user);
        
        // Create payment intent for a $25 booking
        await StripeExamples.createPaymentForBooking(user, 25.00, 123);
        
        // Process complete booking payment workflow
        const result = await StripeExamples.processBookingPayment(user.email, 35.50, 456);
        console.log('\nBooking payment result:', result);
        
        console.log('\n✅ Stripe integration demonstration completed!');
        
    } catch (error) {
        console.error('❌ Demonstration failed:', error);
    }
}
