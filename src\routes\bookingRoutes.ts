import express from "express";
import * as bookingController from "../controllers/bookingController";
import { authenticate, authorize } from "../middleware/auth";

const router = express.Router();

router.get("/", authenticate,  bookingController.getAllBookings);

// Get team availability for a specific date (admin/manager only)
router.get(
    "/availability/:date",
    authenticate,
    authorize(["admin", "manager"]),
    bookingController.getTeamAvailability
);

// Get booking statistics (admin/manager only)
router.get(
    "/statistics",
    authenticate,
    authorize(["admin", "manager"]),
    bookingController.getBookingStatistics
);

router.get("/:id", authenticate, bookingController.getBookingById);

router.put(
    "/:id/status",
    authenticate,
    authorize(["admin", "manager"]),
    bookingController.updateBookingStatus
);
router.put("/:id/cancel", authenticate, bookingController.cancelBooking);

// Reschedule booking
router.put(
    "/:id/reschedule",
    authenticate,
    bookingController.rescheduleBooking
);

router.post("/", authenticate, bookingController.createBooking);
// Check time slot availability (authenticated users)
router.post(
    "/check-availability",
    authenticate,
    bookingController.checkTimeSlotAvailability
);

export default router;
