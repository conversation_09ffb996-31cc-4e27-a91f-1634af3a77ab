import { Router } from 'express';
import {
    getCustomerInfo,
    createCustomer,
    getPaymentMethods,
    createPaymentIntent,
} from '../controllers/stripeController';
import { authenticate } from '../middleware/auth';

const router = Router();

/**
 * @route GET /api/stripe/customer
 * @desc Get current user's Stripe customer information
 * @access Private
 */
router.get('/customer', authenticate, getCustomerInfo);

/**
 * @route POST /api/stripe/customer
 * @desc Create Stripe customer for current user
 * @access Private
 */
router.post('/customer', createCustomer);

/**
 * @route GET /api/stripe/payment-methods
 * @desc Get payment methods for current user
 * @access Private
 */
router.get('/payment-methods', getPaymentMethods);

/**
 * @route POST /api/stripe/payment-intent
 * @desc Create payment intent for a booking
 * @access Private
 * @body { amount: number, currency?: string, bookingId?: number }
 */
router.post('/payment-intent', createPaymentIntent);

export default router;
