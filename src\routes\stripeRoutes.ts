import express from "express";
import * as stripeController from "../controllers/stripeController";
import { authenticate } from "../middleware/auth";

const router = express.Router();

// Get current user's Stripe customer information (users can view their own)
router.get("/customer", authenticate, stripeController.getCustomerInfo);

// Create Stripe customer for current user (users can create their own)
router.post("/customer", authenticate, stripeController.createCustomer);

// Get payment methods for current user (users can view their own payment methods)
router.get("/payment-methods", authenticate, stripeController.getPaymentMethods);

// Create payment intent for a booking (users can create payments for their bookings)
router.post("/payment-intent", authenticate, stripeController.createPaymentIntent);

export default router;
