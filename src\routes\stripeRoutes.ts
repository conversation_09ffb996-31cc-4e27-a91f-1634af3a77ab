import { Router } from 'express';
import { StripeController } from '../controllers/stripeController';
import { authenticateToken } from '../middleware/auth';

const router = Router();

// All Stripe routes require authentication
router.use(authenticateToken);

/**
 * @route GET /api/stripe/config
 * @desc Get Stripe publishable key
 * @access Private
 */
router.get('/config', StripeController.getPublishableKey);

/**
 * @route GET /api/stripe/customer
 * @desc Get current user's Stripe customer information
 * @access Private
 */
router.get('/customer', StripeController.getCustomerInfo);

/**
 * @route POST /api/stripe/customer
 * @desc Create Stripe customer for current user
 * @access Private
 */
router.post('/customer', StripeController.createCustomer);

/**
 * @route GET /api/stripe/payment-methods
 * @desc Get payment methods for current user
 * @access Private
 */
router.get('/payment-methods', StripeController.getPaymentMethods);

/**
 * @route POST /api/stripe/payment-intent
 * @desc Create payment intent for a booking
 * @access Private
 * @body { amount: number, currency?: string, bookingId?: number }
 */
router.post('/payment-intent', StripeController.createPaymentIntent);

export default router;
