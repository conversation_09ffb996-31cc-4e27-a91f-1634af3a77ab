import { Request, Response } from "express";
import { Service } from "../models";

export const getAllServices = async (req: Request, res: Response) => {
    try {
        const services = await Service.findAllRecords();
        res.status(200).json(services);
    } catch (error) {
        console.error("Error fetching services:", error);
        res.status(500).json({ message: "Error fetching services" });
    }
};

export const getServiceById = async (req: Request, res: Response) => {
    try {
        const { id } = req.params;
        const service = await Service.findById(id);

        if (!service) {
            return res.status(404).json({ message: "Service not found" });
        }

        res.status(200).json(service);
    } catch (error) {
        console.error("Error fetching service:", error);
        res.status(500).json({ message: "Error fetching service" });
    }
};

export const getMainServices = async (req: Request, res: Response) => {
    try {
        const services = await Service.findAllRecords({
            where: { isAdditional: false }
        });
        res.status(200).json(services);
    } catch (error) {
        console.error("Error fetching main services:", error);
        res.status(500).json({ message: "Error fetching main services" });
    }
};

export const getAdditionalServices = async (req: Request, res: Response) => {
    try {
        const services = await Service.findAllRecords({
            where: { isAdditional: true }
        });
        res.status(200).json(services);
    } catch (error) {
        console.error("Error fetching additional services:", error);
        res.status(500).json({ message: "Error fetching additional services" });
    }
};

export const createService = async (req: Request, res: Response) => {
    try {
        const { name, description, price, duration, isAdditional } = req.body;

        const service = await Service.create({
            name,
            description,
            price,
            duration,
            isAdditional: isAdditional || false,
        });

        res.status(201).json({
            message: "Service created successfully",
            service,
        });
    } catch (error) {
        console.error("Error creating service:", error);
        res.status(500).json({ message: "Error creating service" });
    }
};

export const updateService = async (req: Request, res: Response) => {
    try {
        const { id } = req.params;
        const { name, description, price, duration, isAdditional } = req.body;

        const service = await Service.findById(id);

        if (!service) {
            return res.status(404).json({ message: "Service not found" });
        }

        await service.update({
            name,
            description,
            price,
            duration,
            ...(isAdditional !== undefined && { isAdditional }),
        });

        res.status(200).json({
            message: "Service updated successfully",
            service,
        });
    } catch (error) {
        console.error("Error updating service:", error);
        res.status(500).json({ message: "Error updating service" });
    }
};

export const deleteService = async (req: Request, res: Response) => {
    try {
        const { id } = req.params;

        const service = await Service.findById(id);

        if (!service) {
            return res.status(404).json({ message: "Service not found" });
        }

        await service.destroy();

        res.status(200).json({ message: "Service deleted successfully" });
    } catch (error) {
        console.error("Error deleting service:", error);
        res.status(500).json({ message: "Error deleting service" });
    }
};
