import { Request, Response, NextFunction } from "express";
import jwt from "jsonwebtoken";
import jwtConfig from "../config/jwt";
import { User } from "../models";

interface JwtPayload {
    id: number;
    email: string;
    roles: string[];
}

declare global {
    namespace Express {
        interface Request {
            user?: JwtPayload;
        }
    }
}

export const authenticate = async (
    req: Request,
    res: Response,
    next: NextFunction
) => {
    try {
        const authHeader = req.headers.authorization;

        if (!authHeader || !authHeader.startsWith("Bearer ")) {
            return res.status(401).json({ message: "Authentication required" });
        }

        const token = authHeader.split(" ")[1];

        const decoded = jwt.verify(token, jwtConfig.secret) as JwtPayload;

        // Check if user exists and get current roles
        const user = await User.findById(decoded.id);

        if (!user) {
            return res.status(401).json({ message: "User not found" });
        }

        // Get user's current roles from database (in case they've changed since token was issued)
        const currentRoles = await user.getRoles();

        // Update the decoded payload with current roles
        req.user = {
            ...decoded,
            roles: currentRoles,
        };

        next();
    } catch (error) {
        return res.status(401).json({ message: "Invalid token" });
    }
};

export const authorize = (requiredRoles: string[]) => {
    return (req: Request, res: Response, next: NextFunction) => {
        if (!req.user) {
            return res.status(401).json({ message: "Authentication required" });
        }

        // Check if user has any of the required roles
        const hasRequiredRole = requiredRoles.some((role) =>
            req.user!.roles.includes(role)
        );

        if (!hasRequiredRole) {
            return res.status(403).json({
                message:
                    "Access denied. Required roles: " +
                    requiredRoles.join(", "),
                userRoles: req.user.roles,
            });
        }

        next();
    };
};

// New middleware to require ALL specified roles (instead of ANY)
export const authorizeAll = (requiredRoles: string[]) => {
    return (req: Request, res: Response, next: NextFunction) => {
        if (!req.user) {
            return res.status(401).json({ message: "Authentication required" });
        }

        // Check if user has ALL of the required roles
        const hasAllRoles = requiredRoles.every((role) =>
            req.user!.roles.includes(role)
        );

        if (!hasAllRoles) {
            const missingRoles = requiredRoles.filter(
                (role) => !req.user!.roles.includes(role)
            );
            return res.status(403).json({
                message:
                    "Access denied. Missing roles: " + missingRoles.join(", "),
                userRoles: req.user.roles,
            });
        }

        next();
    };
};
