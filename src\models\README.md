# BaseModel Documentation

The `BaseModel` class provides a set of common static methods that all your Sequelize models can inherit. This eliminates code duplication and provides a consistent API across all models.

## Features

- **CRUD Operations**: Create, Read, Update, Delete operations
- **Pagination**: Built-in pagination support with metadata
- **Search**: Text search functionality
- **Bulk Operations**: Bulk create, update, and delete
- **Utility Methods**: Count, exists, find or create, etc.
- **TypeScript Support**: Full type safety with generics

## Usage

All your models (User, Service, Booking) now extend `BaseModel` instead of Sequelize's `Model` directly, giving them access to all these methods:

### Basic CRUD Operations

```typescript
// Find all records
const users = await User.findAllRecords();

// Find by ID
const user = await User.findById(1);

// Create a record
const newUser = await User.createRecord({
    firstName: "John",
    lastName: "Doe",
    email: "<EMAIL>",
    password: "password123",
    phone: "1234567890"
});

// Update by ID
const [affectedCount, updatedUsers] = await User.updateById(1, {
    firstName: "Jane"
});

// Delete by ID
const deletedCount = await User.deleteById(1);
```

### Pagination

```typescript
// Simple pagination
const users = await User.findAllRecords({
    pagination: { page: 1, limit: 10 }
});

// Pagination with metadata
const result = await User.findAllPaginated({
    pagination: { page: 1, limit: 10 },
    where: { role: "user" },
    order: [['createdAt', 'DESC']]
});

console.log(result.data); // Array of users
console.log(result.pagination); // Pagination metadata
```

### Search and Filtering

```typescript
// Search by field (case-insensitive)
const users = await User.search('firstName', 'john');

// Complex filtering
const bookings = await Booking.findAll({
    where: {
        status: 'pending',
        bookingDate: {
            [Op.gte]: new Date()
        }
    },
    include: [User, Service]
});
```

### Utility Methods

```typescript
// Count records
const userCount = await User.countRecords({
    where: { role: 'user' }
});

// Check if exists
const exists = await User.existsById(1);

// Find or create
const [user, created] = await User.findOrCreateRecord({
    where: { email: '<EMAIL>' },
    defaults: {
        firstName: 'John',
        lastName: 'Doe',
        password: 'password123',
        phone: '1234567890'
    }
});

// Get latest records
const latestBookings = await Booking.getLatest(5);
```

### Bulk Operations

```typescript
// Bulk create
const services = await Service.bulkCreateRecords([
    { name: 'Basic Wash', description: 'Basic car wash', price: 25.00, duration: 30 },
    { name: 'Premium Wash', description: 'Premium car wash', price: 45.00, duration: 60 }
]);

// Bulk update
const [affectedCount] = await Booking.bulkUpdateRecords(
    { status: 'confirmed' },
    { where: { id: [1, 2, 3] } }
);

// Bulk delete
const deletedCount = await Booking.bulkDeleteRecords({
    where: { status: 'cancelled' }
});
```

## Available Methods

### Query Methods
- `findAllRecords(options?)` - Find all records with optional pagination
- `findAllPaginated(options?)` - Find all with pagination metadata
- `findById(id, options?)` - Find by primary key
- `findOneRecord(options?)` - Find single record
- `search(field, value, options?)` - Search by field (case-insensitive)
- `getLatest(limit?, options?)` - Get latest records by createdAt

### Create Methods
- `createRecord(values, options?)` - Create single record
- `bulkCreateRecords(records, options?)` - Create multiple records
- `findOrCreateRecord(options)` - Find existing or create new

### Update Methods
- `updateById(id, values, options?)` - Update by ID
- `bulkUpdateRecords(values, options)` - Update multiple records

### Delete Methods
- `deleteById(id, options?)` - Delete by ID
- `bulkDeleteRecords(options)` - Delete multiple records

### Utility Methods
- `countRecords(options?)` - Count records
- `existsById(id)` - Check if record exists

## Type Safety

All methods are fully typed with TypeScript generics, providing:
- Autocomplete for model attributes
- Type checking for query options
- Proper return types
- IntelliSense support

## Examples

See `src/examples/BaseModelUsage.ts` for comprehensive usage examples.

## Migration from Standard Sequelize

If you have existing code using standard Sequelize methods, you can gradually migrate:

```typescript
// Old way
const users = await User.findAll({ limit: 10, offset: 0 });

// New way (equivalent)
const users = await User.findAll({ pagination: { page: 1, limit: 10 } });

// Or with metadata
const result = await User.findAllPaginated({ pagination: { page: 1, limit: 10 } });
```

All standard Sequelize methods are still available since BaseModel extends the original Model class.
