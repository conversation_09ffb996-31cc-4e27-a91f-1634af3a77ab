/**
 * Test file for the new Role-based Access Control (RBAC) system
 * Run this with: npx ts-node src/test/roleSystemTest.ts
 */

import { User, Role, UserRole } from "../models";
import sequelize from "../config/database";

async function testRoleSystem() {
    try {
        // Test database connection
        await sequelize.authenticate();
        console.log("✅ Database connection established");

        // Test 1: Create roless
        console.log("\n🧪 Test 1: Creating roles");
        const adminRole = await Role.findOrCreateRecord({
            where: { name: "admin" },
            defaults: { name: "admin", description: "Administrator" },
        });

        const userRole = await Role.findOrCreateRecord({
            where: { name: "user" },
            defaults: { name: "user", description: "Regular user" },
        });

        const managerRole = await Role.findOrCreateRecord({
            where: { name: "manager" },
            defaults: { name: "manager", description: "Manager" },
        });

        console.log("✅ Roles created/found");

        // Test 2: Create user and assign roles
        console.log("\n🧪 Test 2: Creating user and assigning roles");

        // Find or create test user
        const [testUser, created] = await User.findOrCreateRecord({
            where: { email: "<EMAIL>" },
            defaults: {
                firstName: "Test",
                lastName: "User",
                email: "<EMAIL>",
                password: "test123",
                phone: "1234567890",
            },
        });

        console.log(
            `✅ User ${created ? "created" : "found"}: ${testUser.email}`
        );

        // Assign roles
        await testUser.assignRole("user");
        await testUser.assignRole("manager");

        console.log("✅ Roles assigned to user");

        // Test 3: Check user roles
        console.log("\n🧪 Test 3: Checking user roles");
        const userRoles = await testUser.getRoles();
        console.log(`✅ User roles: ${userRoles.join(", ")}`);

        // Test 4: Check specific role
        console.log("\n🧪 Test 4: Checking specific roles");
        const hasAdmin = await testUser.hasRole("admin");
        const hasManager = await testUser.hasRole("manager");
        const hasUser = await testUser.hasRole("user");

        console.log(`✅ Has admin role: ${hasAdmin}`);
        console.log(`✅ Has manager role: ${hasManager}`);
        console.log(`✅ Has user role: ${hasUser}`);

        // Test 5: Check multiple roles
        console.log("\n🧪 Test 5: Checking multiple roles");
        const canManage = await testUser.hasAnyRole(["admin", "manager"]);
        console.log(`✅ Can manage (admin or manager): ${canManage}`);

        // Test 6: Get all users with a role
        console.log("\n🧪 Test 6: Getting users with specific role");
        const managersCount = await UserRole.countRecords({
            include: [
                {
                    model: Role,
                    where: { name: "manager" },
                },
            ],
        });
        console.log(`✅ Number of users with manager role: ${managersCount}`);

        // Test 7: Remove role
        console.log("\n🧪 Test 7: Removing role from user");
        await testUser.removeRole("manager");
        const rolesAfterRemoval = await testUser.getRoles();
        console.log(
            `✅ User roles after removing manager: ${rolesAfterRemoval.join(
                ", "
            )}`
        );

        // Test 8: Re-assign role
        console.log("\n🧪 Test 8: Re-assigning role");
        await testUser.assignRole("manager");
        const rolesAfterReassign = await testUser.getRoles();
        console.log(
            `✅ User roles after re-assigning manager: ${rolesAfterReassign.join(
                ", "
            )}`
        );

        // Test 9: Test role-based queries
        console.log("\n🧪 Test 9: Role-based queries");

        // Get all admin users
        const adminUsers = await User.findAll({
            include: [
                {
                    model: Role,
                    where: { name: "admin" },
                    through: { attributes: [] }, // Don't include junction table attributes
                },
            ],
        });
        console.log(`✅ Found ${adminUsers.length} admin users`);

        // Test 10: Bulk operations
        console.log("\n🧪 Test 10: Bulk role operations");

        // Create another test user
        const [testUser2, created2] = await User.findOrCreateRecord({
            where: { email: "<EMAIL>" },
            defaults: {
                firstName: "Test2",
                lastName: "User2",
                email: "<EMAIL>",
                password: "test123",
                phone: "1234567891",
            },
        });

        // Assign multiple roles at once
        const rolesToAssign = ["user", "manager"];
        for (const roleName of rolesToAssign) {
            await testUser2.assignRole(roleName);
        }

        const user2Roles = await testUser2.getRoles();
        console.log(`✅ Test user 2 roles: ${user2Roles.join(", ")}`);

        // Test 11: Role validation
        console.log("\n🧪 Test 11: Role validation");
        try {
            await testUser.assignRole("nonexistent_role");
            console.log("❌ Should have failed for nonexistent role");
        } catch (error) {
            console.log("✅ Correctly failed for nonexistent role");
        }

        // Test 12: UserRole direct operations
        console.log("\n🧪 Test 12: UserRole direct operations");

        const userRoleExists = await UserRole.userHasRole(
            testUser.id,
            userRole[0].id
        );
        console.log(`✅ UserRole.userHasRole check: ${userRoleExists}`);

        const totalUserRoles = await UserRole.countRecords();
        console.log(`✅ Total user-role assignments: ${totalUserRoles}`);

        console.log("\n🎉 All role system tests passed!");
    } catch (error) {
        console.error("❌ Test failed:", error);
    } finally {
        await sequelize.close();
        console.log("🔌 Database connection closed");
    }
}

// Run the test
if (require.main === module) {
    testRoleSystem();
}

export default testRoleSystem;
