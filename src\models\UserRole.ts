import { DataTypes, Optional } from "sequelize";
import sequelize from "../config/database";
import BaseModel from "./BaseModel";

interface UserRoleAttributes {
    id: number;
    userId: number;
    roleId: number;
    assignedAt?: Date;
    assignedBy?: number; // ID of the user who assigned this role
    createdAt?: Date;
    updatedAt?: Date;
}

interface UserRoleCreationAttributes
    extends Optional<
        UserRoleAttributes,
        "id" | "assignedAt" | "assignedBy" | "createdAt" | "updatedAt"
    > {}

class UserRole
    extends BaseModel<UserRoleAttributes, UserRoleCreationAttributes>
    implements UserRoleAttributes
{
    public id!: number;
    public userId!: number;
    public roleId!: number;
    public assignedAt?: Date;
    public assignedBy?: number;
    public readonly createdAt!: Date;
    public readonly updatedAt!: Date;

    // Static method to assign role to user
    static async assignRoleToUser(
        userId: number,
        roleId: number,
        assignedBy?: number
    ): Promise<UserRole> {
        return this.createRecord({
            userId,
            roleId,
            assignedAt: new Date(),
            assignedBy,
        });
    }

    // Static method to remove role from user
    static async removeRoleFromUser(
        userId: number,
        roleId: number
    ): Promise<number> {
        return this.destroy({
            where: { userId, roleId },
        });
    }

    // Static method to get user's roles
    static async getUserRoles(userId: number): Promise<UserRole[]> {
        return this.findAll({
            where: { userId },
            include: ["Role"],
        });
    }

    // Static method to get users with specific role
    static async getUsersWithRole(roleId: number): Promise<UserRole[]> {
        return this.findAll({
            where: { roleId },
            include: ["User"],
        });
    }

    // Static method to check if user has role
    static async userHasRole(userId: number, roleId: number): Promise<boolean> {
        const userRole = await this.findOne({
            where: { userId, roleId },
        });
        return !!userRole;
    }
}

UserRole.init(
    {
        id: {
            type: DataTypes.INTEGER,
            autoIncrement: true,
            primaryKey: true,
        },
        userId: {
            type: DataTypes.INTEGER,
            allowNull: false,
            references: {
                model: "users",
                key: "id",
            },
            onDelete: "CASCADE",
            onUpdate: "CASCADE",
        },
        roleId: {
            type: DataTypes.INTEGER,
            allowNull: false,
            references: {
                model: "roles",
                key: "id",
            },
            onDelete: "CASCADE",
            onUpdate: "CASCADE",
        },
        assignedAt: {
            type: DataTypes.DATE,
            allowNull: true,
            defaultValue: DataTypes.NOW,
        },
        assignedBy: {
            type: DataTypes.INTEGER,
            allowNull: true,
            references: {
                model: "users",
                key: "id",
            },
            onDelete: "SET NULL",
            onUpdate: "CASCADE",
        },
    },
    {
        sequelize,
        modelName: "UserRole",
        tableName: "user_roles",
        indexes: [
            {
                unique: true,
                fields: ["userId", "roleId"],
                name: "unique_user_role",
            },
            {
                fields: ["userId"],
            },
            {
                fields: ["roleId"],
            },
        ],
    }
);

export default UserRole;
