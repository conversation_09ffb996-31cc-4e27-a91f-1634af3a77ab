import { Request, Response } from 'express';
import { User } from '../models';
import stripeService from '../services/stripeService';

export class StripeController {
    
    /**
     * Get current user's Stripe customer information
     */
    static async getCustomerInfo(req: Request, res: Response): Promise<void> {
        try {
            const userId = (req as any).user?.id;
            
            if (!userId) {
                res.status(401).json({ error: 'User not authenticated' });
                return;
            }

            const user = await User.findById(userId);
            if (!user) {
                res.status(404).json({ error: 'User not found' });
                return;
            }

            if (!user.stripeCustomerId) {
                res.status(404).json({ 
                    error: 'Stripe customer not found',
                    message: 'User does not have a Stripe customer account'
                });
                return;
            }

            const stripeCustomer = await user.getStripeCustomer();
            
            res.json({
                success: true,
                customer: {
                    id: stripeCustomer.id,
                    email: stripeCustomer.email,
                    name: stripeCustomer.name,
                    phone: stripeCustomer.phone,
                    created: stripeCustomer.created,
                    metadata: stripeCustomer.metadata
                }
            });
        } catch (error) {
            console.error('Error getting customer info:', error);
            res.status(500).json({ 
                error: 'Failed to get customer information',
                message: error instanceof Error ? error.message : 'Unknown error'
            });
        }
    }

    /**
     * Create Stripe customer for current user if not exists
     */
    static async createCustomer(req: Request, res: Response): Promise<void> {
        try {
            const userId = (req as any).user?.id;
            
            if (!userId) {
                res.status(401).json({ error: 'User not authenticated' });
                return;
            }

            const user = await User.findById(userId);
            if (!user) {
                res.status(404).json({ error: 'User not found' });
                return;
            }

            if (user.stripeCustomerId) {
                res.status(400).json({ 
                    error: 'Customer already exists',
                    customerId: user.stripeCustomerId
                });
                return;
            }

            const stripeCustomerId = await user.ensureStripeCustomer();
            
            res.json({
                success: true,
                message: 'Stripe customer created successfully',
                customerId: stripeCustomerId
            });
        } catch (error) {
            console.error('Error creating customer:', error);
            res.status(500).json({ 
                error: 'Failed to create customer',
                message: error instanceof Error ? error.message : 'Unknown error'
            });
        }
    }

    /**
     * Get payment methods for current user
     */
    static async getPaymentMethods(req: Request, res: Response): Promise<void> {
        try {
            const userId = (req as any).user?.id;
            
            if (!userId) {
                res.status(401).json({ error: 'User not authenticated' });
                return;
            }

            const user = await User.findById(userId);
            if (!user) {
                res.status(404).json({ error: 'User not found' });
                return;
            }

            if (!user.stripeCustomerId) {
                res.status(404).json({ 
                    error: 'Stripe customer not found',
                    message: 'User does not have a Stripe customer account'
                });
                return;
            }

            const paymentMethods = await user.getPaymentMethods();
            
            res.json({
                success: true,
                paymentMethods: paymentMethods.map(pm => ({
                    id: pm.id,
                    type: pm.type,
                    card: pm.card ? {
                        brand: pm.card.brand,
                        last4: pm.card.last4,
                        exp_month: pm.card.exp_month,
                        exp_year: pm.card.exp_year
                    } : null,
                    created: pm.created
                }))
            });
        } catch (error) {
            console.error('Error getting payment methods:', error);
            res.status(500).json({ 
                error: 'Failed to get payment methods',
                message: error instanceof Error ? error.message : 'Unknown error'
            });
        }
    }

    /**
     * Create payment intent for a booking
     */
    static async createPaymentIntent(req: Request, res: Response): Promise<void> {
        try {
            const userId = (req as any).user?.id;
            const { amount, currency = 'usd', bookingId } = req.body;
            
            if (!userId) {
                res.status(401).json({ error: 'User not authenticated' });
                return;
            }

            if (!amount || amount <= 0) {
                res.status(400).json({ error: 'Valid amount is required' });
                return;
            }

            const user = await User.findById(userId);
            if (!user) {
                res.status(404).json({ error: 'User not found' });
                return;
            }

            // Ensure user has a Stripe customer
            const stripeCustomerId = await user.ensureStripeCustomer();

            const paymentIntent = await stripeService.createPaymentIntent(
                Math.round(amount * 100), // Convert to cents
                currency,
                stripeCustomerId,
                {
                    userId: userId.toString(),
                    bookingId: bookingId?.toString() || '',
                }
            );
            
            res.json({
                success: true,
                paymentIntent: {
                    id: paymentIntent.id,
                    client_secret: paymentIntent.client_secret,
                    amount: paymentIntent.amount,
                    currency: paymentIntent.currency,
                    status: paymentIntent.status
                }
            });
        } catch (error) {
            console.error('Error creating payment intent:', error);
            res.status(500).json({ 
                error: 'Failed to create payment intent',
                message: error instanceof Error ? error.message : 'Unknown error'
            });
        }
    }

    /**
     * Get Stripe publishable key for frontend
     */
    static async getPublishableKey(req: Request, res: Response): Promise<void> {
        try {
            const publishableKey = process.env.STRIPE_PUBLISHABLE_KEY;
            
            if (!publishableKey) {
                res.status(500).json({ error: 'Stripe publishable key not configured' });
                return;
            }
            
            res.json({
                success: true,
                publishableKey
            });
        } catch (error) {
            console.error('Error getting publishable key:', error);
            res.status(500).json({ 
                error: 'Failed to get publishable key',
                message: error instanceof Error ? error.message : 'Unknown error'
            });
        }
    }
}
