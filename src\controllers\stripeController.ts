import { Request, Response } from "express";
import { User, Booking } from "../models";
import stripeService from "../services/stripeService";

/**
 * Get current user's Stripe customer information
 */
export const getCustomerInfo = async (
    req: Request,
    res: Response
): Promise<void> => {
    try {
        const userId = req.user?.id;

        if (!userId) {
            res.status(401).json({ error: "User not authenticated" });
            return;
        }

        const user = await User.findById(userId);
        if (!user) {
            res.status(404).json({ error: "User not found" });
            return;
        }

        if (!user.stripeCustomerId) {
            res.status(404).json({
                error: "Stripe customer not found",
                message: "User does not have a Stripe customer account",
            });
            return;
        }

        const stripeCustomer = await user.getStripeCustomer();

        res.json({
            success: true,
            customer: {
                id: stripeCustomer.id,
                email: stripeCustomer.email,
                name: stripeCustomer.name,
                phone: stripeCustomer.phone,
                created: stripeCustomer.created,
                metadata: stripeCustomer.metadata,
            },
        });
    } catch (error) {
        console.error("Error getting customer info:", error);
        res.status(500).json({
            error: "Failed to get customer information",
            message: error instanceof Error ? error.message : "Unknown error",
        });
    }
};

/**
 * Create Stripe customer for current user if not exists
 */
export const createCustomer = async (
    req: Request,
    res: Response
): Promise<void> => {
    try {
        const userId = req.user?.id;

        if (!userId) {
            res.status(401).json({ error: "User not authenticated" });
            return;
        }

        const user = await User.findById(userId);
        if (!user) {
            res.status(404).json({ error: "User not found" });
            return;
        }

        if (user.stripeCustomerId) {
            res.status(400).json({
                error: "Customer already exists",
                customerId: user.stripeCustomerId,
            });
            return;
        }

        const stripeCustomerId = await user.ensureStripeCustomer();

        res.json({
            success: true,
            message: "Stripe customer created successfully",
            customerId: stripeCustomerId,
        });
    } catch (error) {
        console.error("Error creating customer:", error);
        res.status(500).json({
            error: "Failed to create customer",
            message: error instanceof Error ? error.message : "Unknown error",
        });
    }
};

/**
 * Get payment methods for current user
 */
export const getPaymentMethods = async (
    req: Request,
    res: Response
): Promise<void> => {
    try {
        const userId = req.user?.id;

        if (!userId) {
            res.status(401).json({ error: "User not authenticated" });
            return;
        }

        const user = await User.findById(userId);
        if (!user) {
            res.status(404).json({ error: "User not found" });
            return;
        }

        if (!user.stripeCustomerId) {
            res.status(404).json({
                error: "Stripe customer not found",
                message: "User does not have a Stripe customer account",
            });
            return;
        }

        const paymentMethods = await user.getPaymentMethods();

        res.json({
            success: true,
            paymentMethods: paymentMethods.map((pm) => ({
                id: pm.id,
                type: pm.type,
                card: pm.card
                    ? {
                          brand: pm.card.brand,
                          last4: pm.card.last4,
                          exp_month: pm.card.exp_month,
                          exp_year: pm.card.exp_year,
                      }
                    : null,
                created: pm.created,
            })),
        });
    } catch (error) {
        console.error("Error getting payment methods:", error);
        res.status(500).json({
            error: "Failed to get payment methods",
            message: error instanceof Error ? error.message : "Unknown error",
        });
    }
};



/**
 * Get Stripe publishable key for frontend
 */
export const getPublishableKey = async (
    _req: Request,
    res: Response
): Promise<void> => {
    try {
        const publishableKey = process.env.STRIPE_PUBLISHABLE_KEY;

        if (!publishableKey) {
            res.status(500).json({
                error: "Stripe publishable key not configured",
            });
            return;
        }

        res.json({
            success: true,
            publishableKey,
        });
    } catch (error) {
        console.error("Error getting publishable key:", error);
        res.status(500).json({
            error: "Failed to get publishable key",
            message: error instanceof Error ? error.message : "Unknown error",
        });
    }
};

/**
 * Handle Stripe webhook events
 */
export const handleWebhook = async (req: Request, res: Response): Promise<void> => {
    try {
        const sig = req.headers['stripe-signature'] as string;
        const webhookSecret = process.env.STRIPE_WEBHOOK_SECRET;

        if (!webhookSecret) {
            console.error('Stripe webhook secret not configured');
            res.status(500).json({ error: 'Webhook secret not configured' });
            return;
        }

        if (!sig) {
            res.status(400).json({ error: 'Missing stripe-signature header' });
            return;
        }

        // Verify webhook signature
        const stripe = stripeService.getStripeInstance();
        let event;

        try {
            event = stripe.webhooks.constructEvent(req.body, sig, webhookSecret);
        } catch (err) {
            console.error('Webhook signature verification failed:', err);
            res.status(400).json({ error: 'Invalid signature' });
            return;
        }

        // Handle the event
        switch (event.type) {
            case 'payment_intent.succeeded':
                await handlePaymentSuccess(event.data.object);
                break;
            case 'payment_intent.payment_failed':
                await handlePaymentFailure(event.data.object);
                break;
            default:
                console.log(`Unhandled event type: ${event.type}`);
        }

        res.json({ received: true });
    } catch (error) {
        console.error('Error handling webhook:', error);
        res.status(500).json({
            error: 'Webhook handler failed',
            message: error instanceof Error ? error.message : 'Unknown error'
        });
    }
};

/**
 * Handle successful payment
 */
async function handlePaymentSuccess(paymentIntent: any): Promise<void> {
    try {
        const bookingId = paymentIntent.metadata?.bookingId;

        if (!bookingId) {
            console.error('No booking ID found in payment intent metadata');
            return;
        }

        // Find the booking
        const booking = await Booking.findById(parseInt(bookingId));

        if (!booking) {
            console.error(`Booking not found: ${bookingId}`);
            return;
        }

        // Update booking status to confirmed
        await booking.update({ status: 'confirmed' });

        console.log(`Payment successful for booking ${bookingId}. Status updated to confirmed.`);

        // Here you could add additional logic like:
        // - Send confirmation email to customer
        // - Notify the assigned team
        // - Update inventory or other systems

    } catch (error) {
        console.error('Error handling payment success:', error);
    }
}

/**
 * Handle failed payment
 */
async function handlePaymentFailure(paymentIntent: any): Promise<void> {
    try {
        const bookingId = paymentIntent.metadata?.bookingId;

        if (!bookingId) {
            console.error('No booking ID found in payment intent metadata');
            return;
        }

        // Find the booking
        const booking = await Booking.findById(parseInt(bookingId));

        if (!booking) {
            console.error(`Booking not found: ${bookingId}`);
            return;
        }

        // Update booking status to cancelled due to payment failure
        await booking.update({ status: 'cancelled' });

        console.log(`Payment failed for booking ${bookingId}. Status updated to cancelled.`);

        // Here you could add additional logic like:
        // - Send payment failure notification to customer
        // - Release the team assignment
        // - Log the failure for analytics

    } catch (error) {
        console.error('Error handling payment failure:', error);
    }
}