# Stripe Integration Documentation

## Overview

This document describes the Stripe integration implemented in the Mobile Carwash Server. The integration automatically creates Stripe customers when users are created and provides a comprehensive API for payment processing.

## Features

- **Automatic Customer Creation**: When a new user is created, a corresponding Stripe customer is automatically created via the `afterCreate` hook
- **Customer Management**: Full CRUD operations for Stripe customers
- **Payment Methods**: Retrieve and manage customer payment methods
- **Payment Intents**: Create payment intents for bookings
- **Error Handling**: Comprehensive error handling with fallback mechanisms

## Architecture

### Files Structure

```
src/
├── services/
│   └── stripeService.ts          # Core Stripe service with all Stripe operations
├── controllers/
│   └── stripeController.ts       # API endpoints for Stripe operations
├── routes/
│   └── stripeRoutes.ts          # Route definitions for Stripe endpoints
├── config/
│   └── stripe.ts                # Stripe configuration
├── models/
│   └── User.ts                  # Updated with Stripe integration
└── test/
    └── stripeIntegrationTest.ts # Test file for Stripe integration
```

## Database Changes

### User Model Updates

The `User` model has been updated with:

- **New Field**: `stripeCustomerId` (optional string, unique)
- **New Hook**: `afterCreate` hook that automatically creates Stripe customers
- **New Methods**:
  - `getStripeCustomer()`: Retrieve Stripe customer data
  - `ensureStripeCustomer()`: Create Stripe customer if not exists
  - `getPaymentMethods()`: Get customer's payment methods

## Environment Variables

Add these environment variables to your `.env` file:

```env
# Stripe Configuration
STRIPE_SECRET_KEY=sk_test_your_stripe_secret_key_here
STRIPE_PUBLISHABLE_KEY=pk_test_your_stripe_publishable_key_here
STRIPE_WEBHOOK_SECRET=whsec_your_webhook_secret_here
STRIPE_CURRENCY=usd
```

## API Endpoints

All Stripe endpoints require authentication and are prefixed with `/api/stripe/`.

### GET /api/stripe/config
Get Stripe publishable key for frontend integration.

**Response:**
```json
{
  "success": true,
  "publishableKey": "pk_test_..."
}
```

### GET /api/stripe/customer
Get current user's Stripe customer information.

**Response:**
```json
{
  "success": true,
  "customer": {
    "id": "cus_...",
    "email": "<EMAIL>",
    "name": "John Doe",
    "phone": "+1234567890",
    "created": 1640995200,
    "metadata": {}
  }
}
```

### POST /api/stripe/customer
Create Stripe customer for current user (if not exists).

**Response:**
```json
{
  "success": true,
  "message": "Stripe customer created successfully",
  "customerId": "cus_..."
}
```

### GET /api/stripe/payment-methods
Get payment methods for current user.

**Response:**
```json
{
  "success": true,
  "paymentMethods": [
    {
      "id": "pm_...",
      "type": "card",
      "card": {
        "brand": "visa",
        "last4": "4242",
        "exp_month": 12,
        "exp_year": 2025
      },
      "created": 1640995200
    }
  ]
}
```

### POST /api/stripe/payment-intent
Create payment intent for a booking.

**Request Body:**
```json
{
  "amount": 25.00,
  "currency": "usd",
  "bookingId": 123
}
```

**Response:**
```json
{
  "success": true,
  "paymentIntent": {
    "id": "pi_...",
    "client_secret": "pi_..._secret_...",
    "amount": 2500,
    "currency": "usd",
    "status": "requires_payment_method"
  }
}
```

## Usage Examples

### Creating a User (Automatic Stripe Customer Creation)

```typescript
import { User } from './models';

// This will automatically create a Stripe customer via the afterCreate hook
const user = await User.createRecord({
  firstName: 'John',
  lastName: 'Doe',
  email: '<EMAIL>',
  password: 'securepassword',
  phone: '+1234567890'
});

// After creation, user.stripeCustomerId will be populated
console.log(user.stripeCustomerId); // "cus_..."
```

### Manual Stripe Customer Creation

```typescript
// If for some reason the automatic creation failed, you can ensure it exists
const stripeCustomerId = await user.ensureStripeCustomer();
```

### Getting Customer Information

```typescript
const stripeCustomer = await user.getStripeCustomer();
console.log(stripeCustomer.email);
```

### Creating Payment Intent

```typescript
import stripeService from './services/stripeService';

const paymentIntent = await stripeService.createPaymentIntent(
  2500, // $25.00 in cents
  'usd',
  user.stripeCustomerId,
  { bookingId: '123' }
);
```

## Error Handling

The integration includes comprehensive error handling:

1. **Graceful Failures**: If Stripe customer creation fails during user creation, the user creation still succeeds
2. **Retry Mechanism**: The `ensureStripeCustomer()` method can be used to retry customer creation
3. **Validation**: All required environment variables are validated
4. **Logging**: Detailed logging for debugging and monitoring

## Testing

Run the Stripe integration test:

```bash
npx ts-node src/test/stripeIntegrationTest.ts
```

This test will:
- Create test users
- Verify Stripe customer creation
- Test payment intent creation
- Clean up test data

## Security Considerations

1. **API Keys**: Never expose secret keys in frontend code
2. **Webhooks**: Implement webhook signature verification for production
3. **Amount Validation**: Always validate payment amounts on the server
4. **Customer Verification**: Ensure users can only access their own Stripe data

## Future Enhancements

Potential future improvements:

1. **Webhook Handling**: Implement Stripe webhook endpoints for payment status updates
2. **Subscription Management**: Add support for recurring payments/subscriptions
3. **Refunds**: Implement refund functionality
4. **Payment Method Management**: Add endpoints to add/remove payment methods
5. **Invoice Generation**: Generate invoices for completed bookings

## Troubleshooting

### Common Issues

1. **"STRIPE_SECRET_KEY is not defined"**
   - Ensure your `.env` file contains the Stripe secret key
   - Restart your server after adding environment variables

2. **"User does not have a Stripe customer ID"**
   - Use the `ensureStripeCustomer()` method to create one
   - Check if the `afterCreate` hook is working properly

3. **"Failed to create Stripe customer"**
   - Verify your Stripe API keys are correct
   - Check your Stripe account status
   - Review the error logs for specific Stripe error messages
