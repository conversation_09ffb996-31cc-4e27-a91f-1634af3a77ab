# Stripe Integration Documentation

## Overview

This document describes the Stripe integration implemented in the Mobile Carwash Server. The integration automatically creates Stripe customers when users are created and provides a comprehensive API for payment processing.

## Features

- **Automatic Customer Creation**: When a new user is created, a corresponding Stripe customer is automatically created via the `afterCreate` hook
- **Customer Management**: Full CRUD operations for Stripe customers
- **Payment Methods**: Retrieve and manage customer payment methods
- **Payment Intents**: Create payment intents for bookings
- **Error Handling**: Comprehensive error handling with fallback mechanisms

## Architecture

### Files Structure

```
src/
├── services/
│   └── stripeService.ts          # Core Stripe service with all Stripe operations
├── controllers/
│   └── stripeController.ts       # API endpoints for Stripe operations
├── routes/
│   └── stripeRoutes.ts          # Route definitions for Stripe endpoints
├── config/
│   └── stripe.ts                # Stripe configuration
├── models/
│   └── User.ts                  # Updated with Stripe integration
└── test/
    └── stripeIntegrationTest.ts # Test file for Stripe integration
```

## Database Changes

### User Model Updates

The `User` model has been updated with:

- **New Field**: `stripeCustomerId` (optional string, unique)
- **New Hook**: `afterCreate` hook that automatically creates Stripe customers
- **New Methods**:
  - `getStripeCustomer()`: Retrieve Stripe customer data
  - `ensureStripeCustomer()`: Create Stripe customer if not exists
  - `getPaymentMethods()`: Get customer's payment methods

## Environment Variables

Add these environment variables to your `.env` file:

```env
# Stripe Configuration
STRIPE_SECRET_KEY=sk_test_your_stripe_secret_key_here
STRIPE_PUBLISHABLE_KEY=pk_test_your_stripe_publishable_key_here
STRIPE_WEBHOOK_SECRET=whsec_your_webhook_secret_here
STRIPE_CURRENCY=bgn
```

## API Endpoints

All Stripe endpoints require authentication and are prefixed with `/api/stripe/`, except for the webhook endpoint.

### GET /api/stripe/config
Get Stripe publishable key for frontend integration.

**Response:**
```json
{
  "success": true,
  "publishableKey": "pk_test_..."
}
```

### GET /api/stripe/customer
Get current user's Stripe customer information.

**Response:**
```json
{
  "success": true,
  "customer": {
    "id": "cus_...",
    "email": "<EMAIL>",
    "name": "John Doe",
    "phone": "+1234567890",
    "created": 1640995200,
    "metadata": {}
  }
}
```

### POST /api/stripe/customer
Create Stripe customer for current user (if not exists).

**Response:**
```json
{
  "success": true,
  "message": "Stripe customer created successfully",
  "customerId": "cus_..."
}
```

### GET /api/stripe/payment-methods
Get payment methods for current user.

**Response:**
```json
{
  "success": true,
  "paymentMethods": [
    {
      "id": "pm_...",
      "type": "card",
      "card": {
        "brand": "visa",
        "last4": "4242",
        "exp_month": 12,
        "exp_year": 2025
      },
      "created": 1640995200
    }
  ]
}
```

### POST /api/stripe/webhook
Handle Stripe webhook events (no authentication required).

**Headers:**
- `stripe-signature`: Webhook signature for verification

**Supported Events:**
- `payment_intent.succeeded`: Updates booking status to "confirmed"
- `payment_intent.payment_failed`: Updates booking status to "cancelled"

**Response:**
```json
{
  "received": true
}
```

## Integrated Booking Payment Flow

The Stripe integration is seamlessly integrated into the booking creation process:

### How It Works

1. **User creates booking** via `POST /api/bookings` with service IDs
2. **System calculates total price** from selected services
3. **Booking is created** with "pending" status
4. **Payment intent is created** automatically with booking details
5. **Frontend receives** client secret to complete payment
6. **Webhook updates** booking status based on payment result

### Booking Creation with Payment

**Request to `POST /api/bookings`:**
```json
{
  "serviceIds": [1, 3],
  "bookingDate": "2024-01-15",
  "startTime": "2024-01-15T10:00:00Z",
  "address": "123 Main St",
  "notes": "Please call when arriving"
}
```

**Response:**
```json
{
  "message": "Booking created successfully - payment required to confirm",
  "booking": {
    "id": 123,
    "status": "pending",
    "startTime": "2024-01-15T10:00:00Z",
    "endTime": "2024-01-15T11:30:00Z",
    "services": [
      {"name": "Premium Wash", "price": "25.00"},
      {"name": "Wax Polish", "price": "15.00"}
    ]
  },
  "assignedTeam": {
    "id": 2,
    "message": "Assigned to team ID 2"
  },
  "payment": {
    "clientSecret": "pi_..._secret_...",
    "paymentIntentId": "pi_...",
    "amount": 40.00,
    "currency": "usd",
    "status": "requires_payment_method",
    "description": "Mobile Car Wash - Premium Wash, Wax Polish"
  },
  "instructions": "Complete payment using the provided client secret to confirm your booking"
}
```

### Payment Status Flow

1. **Booking Created**: Status = "pending", payment required
2. **Payment Successful**: Webhook updates status to "confirmed"
3. **Payment Failed**: Webhook updates status to "cancelled"

## Usage Examples

### Creating a User (Automatic Stripe Customer Creation)

```typescript
import { User } from './models';

// This will automatically create a Stripe customer via the afterCreate hook
const user = await User.createRecord({
  firstName: 'John',
  lastName: 'Doe',
  email: '<EMAIL>',
  password: 'securepassword',
  phone: '+1234567890'
});

// After creation, user.stripeCustomerId will be populated
console.log(user.stripeCustomerId); // "cus_..."
```

### Manual Stripe Customer Creation

```typescript
// If for some reason the automatic creation failed, you can ensure it exists
const stripeCustomerId = await user.ensureStripeCustomer();
```

### Getting Customer Information

```typescript
const stripeCustomer = await user.getStripeCustomer();
console.log(stripeCustomer.email);
```

### Creating Payment Intent

```typescript
import stripeService from './services/stripeService';

const paymentIntent = await stripeService.createPaymentIntent(
  2500, // $25.00 in cents
  'usd',
  user.stripeCustomerId,
  { bookingId: '123' }
);
```

## Error Handling

The integration includes comprehensive error handling:

1. **Graceful Failures**: If Stripe customer creation fails during user creation, the user creation still succeeds
2. **Retry Mechanism**: The `ensureStripeCustomer()` method can be used to retry customer creation
3. **Validation**: All required environment variables are validated
4. **Logging**: Detailed logging for debugging and monitoring

## Testing

Run the Stripe integration test:

```bash
npx ts-node src/test/stripeIntegrationTest.ts
```

This test will:
- Create test users
- Verify Stripe customer creation
- Test payment intent creation
- Clean up test data

## Security Considerations

1. **API Keys**: Never expose secret keys in frontend code
2. **Webhooks**: Implement webhook signature verification for production
3. **Amount Validation**: Always validate payment amounts on the server
4. **Customer Verification**: Ensure users can only access their own Stripe data

## Future Enhancements

Potential future improvements:

1. **Webhook Handling**: Implement Stripe webhook endpoints for payment status updates
2. **Subscription Management**: Add support for recurring payments/subscriptions
3. **Refunds**: Implement refund functionality
4. **Payment Method Management**: Add endpoints to add/remove payment methods
5. **Invoice Generation**: Generate invoices for completed bookings

## Troubleshooting

### Common Issues

1. **"STRIPE_SECRET_KEY is not defined"**
   - Ensure your `.env` file contains the Stripe secret key
   - Restart your server after adding environment variables

2. **"User does not have a Stripe customer ID"**
   - Use the `ensureStripeCustomer()` method to create one
   - Check if the `afterCreate` hook is working properly

3. **"Failed to create Stripe customer"**
   - Verify your Stripe API keys are correct
   - Check your Stripe account status
   - Review the error logs for specific Stripe error messages
