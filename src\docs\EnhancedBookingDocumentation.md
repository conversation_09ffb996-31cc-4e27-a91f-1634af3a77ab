# Enhanced Booking System Documentation

## Overview

The enhanced booking system now includes team assignments and time-based scheduling with automatic availability validation. This prevents double-bookings and ensures efficient team utilization.

## New Features

### 1. Team Assignment
- Every booking is now assigned to a team (`teamId`)
- Automatic team assignment based on availability
- Team-based scheduling and conflict prevention

### 2. Time-Based Scheduling
- `startTime` and `endTime` fields for precise scheduling
- Automatic end time calculation based on service duration
- Business hours validation (8 AM - 6 PM)

### 3. Availability Validation
- Real-time team availability checking
- Conflict detection and prevention
- Alternative time suggestions when slots are unavailable

### 4. Enhanced Status Management
- New `in_progress` status for active bookings
- Better status tracking throughout the service lifecycle

## Database Schema Changes

### Booking Model Updates
```typescript
interface BookingAttributes {
    id: number;
    userId: number;
    serviceId: number;
    teamId?: number;           // NEW: Team assignment
    bookingDate: Date;
    startTime: Date;           // NEW: Precise start time
    endTime: Date;             // NEW: Precise end time
    status: "pending" | "confirmed" | "in_progress" | "completed" | "cancelled";
    address: string;
    notes?: string;
    createdAt?: Date;
    updatedAt?: Date;
}
```

## API Endpoints

### Enhanced Booking Creation
```
POST /api/bookings
{
    "serviceId": 1,
    "bookingDate": "2024-01-15",
    "startTime": "2024-01-15T10:00:00Z",
    "address": "123 Main St",
    "notes": "Optional notes"
}
```

**Response:**
- ✅ Success: Booking created with assigned team
- ❌ Conflict: No teams available + suggested times

### New Endpoints

#### Check Time Slot Availability
```
POST /api/bookings/check-availability
{
    "startTime": "2024-01-15T10:00:00Z",
    "duration": 30
}
```

#### Get Team Availability
```
GET /api/bookings/availability/2024-01-15
```

#### Reschedule Booking
```
PUT /api/bookings/:id/reschedule
{
    "newStartTime": "2024-01-15T14:00:00Z"
}
```

#### Booking Statistics
```
GET /api/bookings/statistics?startDate=2024-01-01&endDate=2024-01-31
```

## Booking Workflow

### 1. Create Booking Request
```typescript
const bookingData = {
    serviceId: 1,
    bookingDate: "2024-01-15",
    startTime: "2024-01-15T10:00:00Z",
    address: "123 Main Street",
    notes: "Please call when arriving"
};
```

### 2. System Validation
- ✅ Service exists
- ✅ Time is in the future
- ✅ Within business hours (8 AM - 6 PM)
- ✅ Team availability check

### 3. Team Assignment
- Find available teams for time slot
- Assign first available team
- Create booking with team assignment

### 4. Conflict Resolution
If no teams available:
- Return 409 Conflict status
- Provide suggested alternative times
- Allow user to choose different time

## Availability Logic

### Team Availability Check
```typescript
// Check if team is available for time slot
const isAvailable = await Booking.isTeamAvailable(
    teamId, 
    startTime, 
    endTime, 
    excludeBookingId? // For updates
);
```

### Find Available Teams
```typescript
// Get all available teams for time slot
const availableTeams = await Booking.findAvailableTeams(
    startTime, 
    endTime
);
```

### Conflict Detection
The system checks for overlapping bookings:
- New booking starts during existing booking
- New booking ends during existing booking  
- New booking completely contains existing booking

## Business Rules

### 1. Business Hours
- Bookings only allowed between 8 AM and 6 PM
- Validation prevents bookings outside these hours

### 2. Future Bookings Only
- All bookings must be scheduled for future times
- Past time validation prevents invalid bookings

### 3. Team Capacity
- One team can only handle one booking at a time
- Automatic conflict prevention

### 4. Service Duration
- End time automatically calculated from service duration
- Ensures consistent scheduling

## Status Lifecycle

```
pending → confirmed → in_progress → completed
    ↓
cancelled (from pending or confirmed)
```

### Status Descriptions
- **pending**: Booking created, awaiting confirmation
- **confirmed**: Booking confirmed, team assigned
- **in_progress**: Service currently being performed
- **completed**: Service finished successfully
- **cancelled**: Booking cancelled by user or admin

## Team Management Integration

### Team Assignment Priority
1. First available team gets assigned
2. Teams must be active (`isActive: true`)
3. No manual team selection in basic flow

### Team Utilization Tracking
- Track bookings per team
- Monitor team performance
- Generate utilization reports

## Error Handling

### Common Scenarios

#### No Teams Available
```json
{
    "status": 409,
    "message": "No teams available for the requested time slot",
    "suggestedTimes": [
        "2024-01-15T11:00:00Z",
        "2024-01-15T12:00:00Z"
    ]
}
```

#### Invalid Time Slot
```json
{
    "status": 400,
    "message": "Bookings are only available between 8 AM and 6 PM"
}
```

#### Past Time Booking
```json
{
    "status": 400,
    "message": "Booking time must be in the future"
}
```

## Performance Considerations

### Database Indexes
- `teamId` indexed for fast team queries
- `startTime` and `endTime` indexed for availability checks
- Composite indexes on common query patterns

### Optimization Strategies
- Cache active teams list
- Batch availability checks
- Efficient conflict detection queries

## Monitoring and Analytics

### Key Metrics
- Team utilization rates
- Booking success/failure rates
- Average booking duration
- Peak booking times
- Cancellation rates

### Reports Available
- Daily team schedules
- Team utilization reports
- Booking statistics
- Conflict analysis

## Migration Notes

### Breaking Changes
- `bookingDate` now requires `startTime`
- New required fields: `startTime`, `endTime`
- Optional `teamId` field added

### Backward Compatibility
- Existing bookings will need migration
- API clients must update to include new fields

## Testing

### Test Scenarios
1. **Happy Path**: Available team, valid time
2. **No Teams**: All teams busy
3. **Invalid Time**: Outside business hours
4. **Past Time**: Booking in the past
5. **Rescheduling**: Change booking time
6. **Conflicts**: Overlapping bookings

### Example Test Data
```typescript
// Valid booking request
const validBooking = {
    serviceId: 1,
    bookingDate: "2024-01-15",
    startTime: "2024-01-15T10:00:00Z",
    address: "123 Test Street"
};

// Conflicting time (team busy)
const conflictingTime = "2024-01-15T10:30:00Z";
```

## Future Enhancements

### Planned Features
- Team preferences for customers
- Recurring bookings
- Booking reminders
- Real-time status updates
- Mobile team tracking
- Dynamic pricing based on demand

### Scalability Considerations
- Multi-location support
- Team specialization (service types)
- Advanced scheduling algorithms
- Load balancing across teams
