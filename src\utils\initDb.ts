import sequelize from "../config/database";
import { User, Service, Role, UserRole, Team, TeamUser, Booking, BookingService } from "../models";
import bcrypt from "bcrypt";

const initializeDatabase = async () => {
    try {
        // Sync all models with the database
        await sequelize.sync({ force: true });
        console.log("Database synchronized");

        // Create default roles
        const adminRole = await Role.createRecord({
            name: "admin",
            description: "Administrator with full access to the system",
        });

        const userRole = await Role.createRecord({
            name: "user",
            description: "Regular user with limited access",
        });

        const managerRole = await Role.createRecord({
            name: "manager",
            description: "Manager with access to bookings and services",
        });

        const employeeRole = await Role.createRecord({
            name: "employee",
            description: "Employee with access to assigned bookings",
        });

        console.log("Default roles created");

        // Create admin user
        const adminUser = await User.createRecord({
            firstName: "Admin",
            lastName: "User",
            email: "<EMAIL>",
            password: "admin123",
            phone: "1234567890",
        });

        // Assign admin role to admin user
        await adminUser.assignRole("admin");

        // Create a regular user
        const regularUser = await User.createRecord({
            firstName: "John",
            lastName: "Doe",
            email: "<EMAIL>",
            password: "user123",
            phone: "0987654321",
        });

        // Assign user role to regular user
        await regularUser.assignRole("user");

        console.log("Admin user created");

        // Create sample teams
        const developmentTeam = await Team.createRecord({
            name: "Development Team",
            description: "Software development and technical implementation",
            leaderId: adminUser.id,
            isActive: true
        });

        const operationsTeam = await Team.createRecord({
            name: "Operations Team",
            description: "Day-to-day operations and service delivery",
            leaderId: adminUser.id,
            isActive: true
        });

        const customerServiceTeam = await Team.createRecord({
            name: "Customer Service",
            description: "Customer support and relationship management",
            isActive: true
        });

        console.log("Sample teams created");

        // Add users to teams
        await adminUser.joinTeam(developmentTeam.id, 'lead');
        await adminUser.joinTeam(operationsTeam.id, 'lead');
        await regularUser.joinTeam(customerServiceTeam.id, 'member');

        console.log("Users assigned to teams");

        // Create some initial services
        await Service.bulkCreate([
            {
                name: "Basic Wash",
                description: "Exterior wash including wheels and tires",
                price: 29.99,
                duration: 30,
                isAdditional: false, // Main service
            },
            {
                name: "Premium Wash",
                description: "Exterior wash, wax, and interior vacuum",
                price: 49.99,
                duration: 60,
                isAdditional: false, // Main service
            },
            {
                name: "Deluxe Detail",
                description: "Complete interior and exterior detailing",
                price: 99.99,
                duration: 120,
                isAdditional: false, // Main service
            },
            {
                name: "Polish",
                description: "Additional polish service for extra shine",
                price: 19.99,
                duration: 20,
                isAdditional: true, // Additional service
            },
            {
                name: "Tire Shine",
                description: "Additional tire shine service",
                price: 9.99,
                duration: 10,
                isAdditional: true, // Additional service
            },
            {
                name: "Interior Protection",
                description: "Additional interior protection coating",
                price: 29.99,
                duration: 30,
                isAdditional: true, // Additional service
            },
        ]);

        console.log("Initial services created");

        // Create sample bookings with team assignments
        const tomorrow = new Date();
        tomorrow.setDate(tomorrow.getDate() + 1);
        tomorrow.setHours(10, 0, 0, 0); // 10 AM tomorrow

        const dayAfterTomorrow = new Date();
        dayAfterTomorrow.setDate(dayAfterTomorrow.getDate() + 2);
        dayAfterTomorrow.setHours(14, 0, 0, 0); // 2 PM day after tomorrow

        // Get the created services
        const basicWash = await Service.findOne({ where: { name: "Basic Wash" } });
        const premiumWash = await Service.findOne({ where: { name: "Premium Wash" } });
        const polish = await Service.findOne({ where: { name: "Polish" } });
        const tireShine = await Service.findOne({ where: { name: "Tire Shine" } });

        if (basicWash && premiumWash && polish && tireShine) {
            // Create booking for regular user with basic wash only
            const booking1StartTime = new Date(tomorrow);
            const booking1EndTime = Booking.calculateEndTime(booking1StartTime, basicWash.duration);

            const booking1 = await Booking.createRecord({
                userId: regularUser.id,
                teamId: developmentTeam.id,
                bookingDate: new Date(tomorrow.toISOString().split('T')[0]),
                startTime: booking1StartTime,
                endTime: booking1EndTime,
                status: "confirmed",
                address: "123 Main Street, City, State 12345",
                notes: "Please call when arriving"
            });

            // Add basic wash as main service
            await BookingService.createRecord({
                bookingId: booking1.id,
                serviceId: basicWash.id,
                isMainService: true
            });

            // Create booking for admin user with premium wash + additional services
            const booking2StartTime = new Date(dayAfterTomorrow);
            // Calculate total duration for premium wash + polish + tire shine
            const totalDuration = premiumWash.duration + polish.duration + tireShine.duration;
            const booking2EndTime = Booking.calculateEndTime(booking2StartTime, totalDuration);

            const booking2 = await Booking.createRecord({
                userId: adminUser.id,
                teamId: operationsTeam.id,
                bookingDate: new Date(dayAfterTomorrow.toISOString().split('T')[0]),
                startTime: booking2StartTime,
                endTime: booking2EndTime,
                status: "pending",
                address: "456 Oak Avenue, City, State 12345",
                notes: "Premium service with additional polish and tire shine"
            });

            // Add premium wash as main service
            await BookingService.createRecord({
                bookingId: booking2.id,
                serviceId: premiumWash.id,
                isMainService: true
            });

            // Add polish as additional service
            await BookingService.createRecord({
                bookingId: booking2.id,
                serviceId: polish.id,
                isMainService: false
            });

            // Add tire shine as additional service
            await BookingService.createRecord({
                bookingId: booking2.id,
                serviceId: tireShine.id,
                isMainService: false
            });

            console.log("Sample bookings created with team assignments and services");
        }

        console.log("Database initialization completed successfully");
    } catch (error) {
        console.error("Database initialization failed:", error);
    } finally {
        process.exit();
    }
};

initializeDatabase();
