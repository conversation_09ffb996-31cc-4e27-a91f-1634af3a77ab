import express from "express";
import authRoutes from "./authRoutes";
import userRoutes from "./userRoutes";
import serviceRoutes from "./serviceRoutes";
import bookingRoutes from "./bookingRoutes";
import roleRoutes from "./roleRoutes";
import teamRoutes from "./teamRoutes";
import stripeRoutes from "./stripeRoutes";

const router = express.Router();

router.use("/auth", authRoutes);
router.use("/users", userRoutes);
router.use("/services", serviceRoutes);
router.use("/bookings", bookingRoutes);
router.use("/roles", roleRoutes);
router.use("/teams", teamRoutes);
router.use("/stripe", stripeRoutes);

export default router;
