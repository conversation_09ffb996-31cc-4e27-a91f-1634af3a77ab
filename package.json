{"name": "mobile_carwash_server", "version": "1.0.0", "description": "", "main": "dist/index.js", "scripts": {"test": "echo \"Error: no test specified\" && exit 1", "build": "tsc", "start": "node dist/index.js", "dev": "nodemon --exec ts-node src/index.ts", "init-db": "ts-node src/utils/initDb.ts"}, "repository": {"type": "git", "url": "git+https://github.com/DeyanTodorov17/mobile_carwash_server.git"}, "keywords": [], "author": "", "license": "ISC", "bugs": {"url": "https://github.com/DeyanTodorov17/mobile_carwash_server/issues"}, "homepage": "https://github.com/DeyanTodorov17/mobile_carwash_server#readme", "dependencies": {"@types/express": "^4.17.21", "bcrypt": "^6.0.0", "cors": "^2.8.5", "dotenv": "^16.5.0", "express": "^4.18.2", "jsonwebtoken": "^9.0.2", "pg": "^8.16.0", "pg-hstore": "^2.3.4", "sequelize": "^6.37.7", "stripe": "^18.2.0"}, "devDependencies": {"@types/bcrypt": "^5.0.2", "@types/cors": "^2.8.18", "@types/jsonwebtoken": "^9.0.9", "@types/node": "^22.15.18", "@types/sequelize": "^4.28.20", "nodemon": "^3.1.10", "ts-node": "^10.9.2", "typescript": "^5.8.3"}}