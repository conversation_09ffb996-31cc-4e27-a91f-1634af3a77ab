# Role-Based Access Control (RBAC) System Documentation

## Overview

The mobile carwash application now uses a flexible Role-Based Access Control (RBAC) system that supports many-to-many relationships between users and roles. This replaces the previous single-role system and provides much more granular permission control.

## Database Schema

### Tables

1. **roles** - Stores available roles in the system
2. **user_roles** - Junction table linking users to roles (many-to-many)
3. **users** - User table (role field removed)

### Models

- `Role` - Manages system roles
- `UserRole` - Junction model for user-role relationships
- `User` - Updated to work with multiple roles

## Default Roles

The system comes with four default roles:

- **admin** - Full system access
- **manager** - Manage bookings and services
- **employee** - Handle assigned bookings
- **user** - Regular customer access

## API Endpoints

### Role Management (`/api/roles`)

```
GET    /api/roles                    # Get all roles (admin only)
POST   /api/roles                    # Create new role (admin only)
POST   /api/roles/assign             # Assign role to user (admin only)
POST   /api/roles/remove             # Remove role from user (admin only)
POST   /api/roles/bulk-assign        # Bulk assign roles (admin only)
GET    /api/roles/user/:userId       # Get user's roles (admin or self)
GET    /api/roles/users/:roleName    # Get users with role (admin/manager)
```

### Updated Endpoints

- **Bookings**: Managers can now view/update all bookings
- **Users**: Role-based access controls applied
- **Auth**: JWT tokens now include roles array instead of single role

## Usage Examples

### Creating and Assigning Roles

```typescript
// Create a new role
const role = await Role.createRecord({
    name: "supervisor",
    description: "Supervises employees"
});

// Assign role to user
const user = await User.findById(1);
await user.assignRole("supervisor");

// Check if user has role
const hasRole = await user.hasRole("supervisor");

// Get all user roles
const roles = await user.getRoles();
```

### Authentication & Authorization

```typescript
// JWT payload now includes roles array
const token = jwt.sign(
    { id: user.id, email: user.email, roles: userRoles },
    jwtConfig.secret
);

// Middleware checks for any of the required roles
authorize(["admin", "manager"]) // User needs admin OR manager role

// New middleware for requiring ALL roles
authorizeAll(["manager", "employee"]) // User needs BOTH roles
```

### Permission Checking

```typescript
// Check single role
const isAdmin = await user.hasRole("admin");

// Check multiple roles (ANY)
const canManage = await user.hasAnyRole(["admin", "manager"]);

// Role-based business logic
if (await user.hasRole("admin")) {
    // Admin-specific logic
} else if (await user.hasRole("manager")) {
    // Manager-specific logic
} else {
    // Regular user logic
}
```

## Migration from Old System

### What Changed

1. **User Model**: Removed single `role` field
2. **JWT Tokens**: Now contain `roles` array instead of `role` string
3. **Auth Middleware**: Updated to check role arrays
4. **Controllers**: Updated to use new role checking methods

### Breaking Changes

- JWT tokens from old system are incompatible (users need to re-login)
- Any code checking `req.user.role` needs to be updated to `req.user.roles`
- Database schema changes require migration

### Migration Steps

1. Run database initialization: `npm run init-db`
2. Update any custom code using the old role system
3. Test role assignments and permissions

## Security Considerations

### Role Assignment Audit

The `user_roles` table includes audit fields:
- `assignedAt` - When the role was assigned
- `assignedBy` - Which user assigned the role

### Permission Hierarchy

Roles have an implicit hierarchy for certain operations:
- `admin` > `manager` > `employee` > `user`

### Best Practices

1. **Principle of Least Privilege**: Assign minimum roles needed
2. **Regular Audits**: Review user roles periodically
3. **Role Separation**: Don't combine conflicting roles
4. **Audit Trail**: Track role changes for compliance

## API Examples

### Register User with Roles

```bash
POST /api/auth/register
{
    "firstName": "John",
    "lastName": "Manager",
    "email": "<EMAIL>",
    "password": "password123",
    "phone": "1234567890",
    "roles": ["user", "manager"]
}
```

### Assign Role to User

```bash
POST /api/roles/assign
Authorization: Bearer <admin-token>
{
    "userId": 1,
    "roleName": "manager"
}
```

### Get User's Roles

```bash
GET /api/roles/user/1
Authorization: Bearer <token>
```

### Check Permissions in Frontend

```javascript
// Check if user can perform action
const canManageBookings = user.roles.includes('admin') || 
                         user.roles.includes('manager');

// Show/hide UI elements based on roles
if (user.roles.includes('admin')) {
    // Show admin panel
}
```

## Error Handling

Common errors and their meanings:

- `Role 'xyz' not found` - Trying to assign non-existent role
- `User already has this role` - Attempting to assign existing role
- `Access denied. Required roles: admin, manager` - Insufficient permissions
- `User does not have this role` - Trying to remove non-assigned role

## Performance Considerations

### Optimizations

1. **Role Caching**: User roles are fetched fresh on each auth check
2. **Eager Loading**: Include roles in user queries when needed
3. **Indexing**: Database indexes on user_roles table for performance

### Monitoring

Monitor these metrics:
- Role assignment frequency
- Permission check performance
- Failed authorization attempts

## Testing

Run the role system tests:

```bash
npx ts-node src/test/roleSystemTest.ts
```

See examples in:
- `src/examples/RoleSystemUsage.ts`
- `src/test/roleSystemTest.ts`

## Troubleshooting

### Common Issues

1. **"User not found" errors**: Ensure user exists before role operations
2. **Permission denied**: Check user has required roles
3. **Role not found**: Verify role exists in database
4. **JWT errors**: Users may need to re-login after system update

### Debug Commands

```typescript
// Check user's current roles
const user = await User.findById(userId);
const roles = await user.getRoles();
console.log('User roles:', roles);

// Verify role exists
const role = await Role.findByName('admin');
console.log('Role exists:', !!role);

// Check role assignment
const hasRole = await UserRole.userHasRole(userId, roleId);
console.log('User has role:', hasRole);
```
