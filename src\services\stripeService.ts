import Stripe from 'stripe';
import dotenv from 'dotenv';

dotenv.config();

class StripeService {
    private stripe: Stripe;

    constructor() {
        const stripeSecretKey = process.env.STRIPE_SECRET_KEY;

        if (!stripeSecretKey) {
            throw new Error('STRIPE_SECRET_KEY is not defined in environment variables');
        }

        this.stripe = new Stripe(stripeSecretKey, {
            apiVersion: '2025-05-28.basil', // Use the latest API version
        });
    }

    /**
     * Create a new Stripe customer
     * @param email Customer's email address
     * @param name Customer's full name
     * @param phone Customer's phone number (optional)
     * @param metadata Additional metadata to store with the customer
     * @returns Promise<Stripe.Customer>
     */
    async createCustomer(
        email: string,
        name: string,
        phone?: string,
        metadata?: Record<string, string>
    ): Promise<Stripe.Customer> {
        try {
            const customerData: Stripe.CustomerCreateParams = {
                email,
                name,
                metadata: {
                    source: 'mobile_carwash_app',
                    ...metadata,
                },
            };

            if (phone) {
                customerData.phone = phone;
            }

            const customer = await this.stripe.customers.create(customerData);

            console.log(`Stripe customer created: ${customer.id} for email: ${email}`);
            return customer;
        } catch (error) {
            console.error('Error creating Stripe customer:', error);
            throw new Error(`Failed to create Stripe customer: ${error instanceof Error ? error.message : 'Unknown error'}`);
        }
    }

    /**
     * Retrieve a Stripe customer by ID
     * @param customerId Stripe customer ID
     * @returns Promise<Stripe.Customer>
     */
    async getCustomer(customerId: string): Promise<Stripe.Customer> {
        try {
            const customer = await this.stripe.customers.retrieve(customerId);

            if (customer.deleted) {
                throw new Error('Customer has been deleted');
            }

            return customer as Stripe.Customer;
        } catch (error) {
            console.error('Error retrieving Stripe customer:', error);
            throw new Error(`Failed to retrieve Stripe customer: ${error instanceof Error ? error.message : 'Unknown error'}`);
        }
    }

    /**
     * Update a Stripe customer
     * @param customerId Stripe customer ID
     * @param updateData Data to update
     * @returns Promise<Stripe.Customer>
     */
    async updateCustomer(
        customerId: string,
        updateData: Stripe.CustomerUpdateParams
    ): Promise<Stripe.Customer> {
        try {
            const customer = await this.stripe.customers.update(customerId, updateData);
            console.log(`Stripe customer updated: ${customer.id}`);
            return customer;
        } catch (error) {
            console.error('Error updating Stripe customer:', error);
            throw new Error(`Failed to update Stripe customer: ${error instanceof Error ? error.message : 'Unknown error'}`);
        }
    }

    /**
     * Delete a Stripe customer
     * @param customerId Stripe customer ID
     * @returns Promise<Stripe.DeletedCustomer>
     */
    async deleteCustomer(customerId: string): Promise<Stripe.DeletedCustomer> {
        try {
            const deletedCustomer = await this.stripe.customers.del(customerId);
            console.log(`Stripe customer deleted: ${customerId}`);
            return deletedCustomer;
        } catch (error) {
            console.error('Error deleting Stripe customer:', error);
            throw new Error(`Failed to delete Stripe customer: ${error instanceof Error ? error.message : 'Unknown error'}`);
        }
    }

    /**
     * List payment methods for a customer
     * @param customerId Stripe customer ID
     * @param type Payment method type (optional, defaults to 'card')
     * @returns Promise<Stripe.PaymentMethod[]>
     */
    async listPaymentMethods(
        customerId: string,
        type: Stripe.PaymentMethodListParams.Type = 'card'
    ): Promise<Stripe.PaymentMethod[]> {
        try {
            const paymentMethods = await this.stripe.paymentMethods.list({
                customer: customerId,
                type,
            });

            return paymentMethods.data;
        } catch (error) {
            console.error('Error listing payment methods:', error);
            throw new Error(`Failed to list payment methods: ${error instanceof Error ? error.message : 'Unknown error'}`);
        }
    }

    /**
     * Create a payment intent for a customer
     * @param amount Amount in cents
     * @param currency Currency code (default: 'usd')
     * @param customerId Stripe customer ID
     * @param metadata Additional metadata
     * @returns Promise<Stripe.PaymentIntent>
     */
    async createPaymentIntent(
        amount: number,
        currency: string = 'usd',
        customerId?: string,
        metadata?: Record<string, string>
    ): Promise<Stripe.PaymentIntent> {
        try {
            const paymentIntentData: Stripe.PaymentIntentCreateParams = {
                amount,
                currency,
                metadata: {
                    source: 'mobile_carwash_app',
                    ...metadata,
                },
            };

            if (customerId) {
                paymentIntentData.customer = customerId;
            }

            const paymentIntent = await this.stripe.paymentIntents.create(paymentIntentData);

            console.log(`Payment intent created: ${paymentIntent.id} for amount: ${amount}`);
            return paymentIntent;
        } catch (error) {
            console.error('Error creating payment intent:', error);
            throw new Error(`Failed to create payment intent: ${error instanceof Error ? error.message : 'Unknown error'}`);
        }
    }

    /**
     * Get the Stripe instance for advanced operations
     * @returns Stripe instance
     */
    getStripeInstance(): Stripe {
        return this.stripe;
    }
}

// Export a singleton instance
export default new StripeService();
