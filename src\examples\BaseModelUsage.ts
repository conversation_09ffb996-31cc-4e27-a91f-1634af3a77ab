/**
 * Example usage of BaseModel static methods
 * This file demonstrates how to use the inherited methods from BaseModel
 */

import { User, Service, Booking } from "../models";

export class BaseModelExamples {

    // Example: Find all users with pagination
    static async getAllUsersWithPagination() {
        const result = await User.findAllPaginated({
            pagination: { page: 1, limit: 10 },
            order: [['createdAt', 'DESC']]
        });

        console.log('Users:', result.data);
        console.log('Pagination info:', result.pagination);
        return result;
    }

    // Example: Find user by ID
    static async getUserById(id: number) {
        const user = await User.findById(id, {
            attributes: ['id', 'firstName', 'lastName', 'email', 'role']
        });

        if (!user) {
            throw new Error('User not found');
        }

        return user;
    }

    // Example: Create a new service
    static async createNewService(serviceData: {
        name: string;
        description: string;
        price: number;
        duration: number;
    }) {
        const service = await Service.createRecord(serviceData);
        console.log('Created service:', service.toJSON());
        return service;
    }

    // Example: Update user by ID
    static async updateUserById(id: number, updateData: Partial<{
        firstName: string;
        lastName: string;
        phone: string;
    }>) {
        const [affectedCount, updatedUsers] = await User.updateById(id, updateData);

        if (affectedCount === 0) {
            throw new Error('User not found or no changes made');
        }

        return updatedUsers[0];
    }

    // Example: Delete booking by ID
    static async deleteBookingById(id: number) {
        const deletedCount = await Booking.deleteById(id);

        if (deletedCount === 0) {
            throw new Error('Booking not found');
        }

        console.log(`Deleted ${deletedCount} booking(s)`);
        return deletedCount;
    }

    // Example: Count services
    static async countActiveServices() {
        const count = await Service.countRecords({
            where: {
                // Add any conditions here, e.g., active: true
            }
        });

        console.log(`Total services: ${count}`);
        return count;
    }

    // Example: Check if user exists
    static async checkUserExists(id: number) {
        const exists = await User.existsById(id);
        console.log(`User ${id} exists: ${exists}`);
        return exists;
    }

    // Example: Find or create user
    static async findOrCreateUser(email: string, userData: {
        firstName: string;
        lastName: string;
        password: string;
        phone: string;
        roles?: string[];
    }) {
        const [user, created] = await User.findOrCreateRecord({
            where: { email },
            defaults: {
                firstName: userData.firstName,
                lastName: userData.lastName,
                password: userData.password,
                phone: userData.phone,
                email
            }
        });

        // Assign roles after user creation
        if (created && userData.roles) {
            for (const roleName of userData.roles) {
                await user.assignRole(roleName);
            }
        }

        console.log(`User ${created ? 'created' : 'found'}:`, user.toJSON());
        return { user, created };
    }

    // Example: Search users by name
    static async searchUsersByName(searchTerm: string) {
        const users = await User.search('firstName', searchTerm, {
            attributes: ['id', 'firstName', 'lastName', 'email'],
            limit: 20
        });

        console.log(`Found ${users.length} users matching "${searchTerm}"`);
        return users;
    }

    // Example: Get latest bookings
    static async getLatestBookings(limit: number = 5) {
        const bookings = await Booking.getLatest(limit, {
            include: [
                { model: User, attributes: ['firstName', 'lastName', 'email'] },
                { model: Service, attributes: ['name', 'price'] }
            ]
        });

        console.log(`Latest ${bookings.length} bookings:`, bookings);
        return bookings;
    }

    // Example: Bulk create services
    static async bulkCreateServices(servicesData: Array<{
        name: string;
        description: string;
        price: number;
        duration: number;
    }>) {
        const services = await Service.bulkCreateRecords(servicesData, {
            validate: true,
            returning: true
        });

        console.log(`Created ${services.length} services`);
        return services;
    }

    // Example: Bulk update bookings status
    static async bulkUpdateBookingStatus(status: "confirmed" | "cancelled", bookingIds: number[]) {
        const [affectedCount] = await Booking.bulkUpdateRecords(
            { status },
            {
                where: { id: bookingIds },
                returning: false
            }
        );

        console.log(`Updated ${affectedCount} bookings to status: ${status}`);
        return affectedCount;
    }

    // Example: Complex query with pagination
    static async getBookingsWithFilters(filters: {
        status?: string;
        userId?: number;
        dateFrom?: Date;
        dateTo?: Date;
        page?: number;
        limit?: number;
    }) {
        const { Op } = require('sequelize');
        const whereClause: any = {};

        if (filters.status) {
            whereClause.status = filters.status;
        }

        if (filters.userId) {
            whereClause.userId = filters.userId;
        }

        if (filters.dateFrom || filters.dateTo) {
            whereClause.bookingDate = {};
            if (filters.dateFrom) {
                whereClause.bookingDate[Op.gte] = filters.dateFrom;
            }
            if (filters.dateTo) {
                whereClause.bookingDate[Op.lte] = filters.dateTo;
            }
        }

        const result = await Booking.findAllPaginated({
            where: whereClause,
            pagination: {
                page: filters.page || 1,
                limit: filters.limit || 10
            },
            include: [
                { model: User, attributes: ['firstName', 'lastName', 'email'] },
                { model: Service, attributes: ['name', 'price', 'duration'] }
            ],
            order: [['bookingDate', 'DESC']]
        });

        return result;
    }
}
