import {
    Model,
    ModelStatic,
    FindOptions,
    CreateOptions,
    UpdateOptions,
    DestroyOptions,
    CountOptions,
    WhereOptions,
    Order,
    Attributes,
    CreationAttributes,
    Op
} from "sequelize";

// Interface for pagination options
export interface PaginationOptions {
    page?: number;
    limit?: number;
    offset?: number;
}

// Interface for query options with pagination
export interface QueryOptions<T = any>
    extends Omit<FindOptions<T>, "limit" | "offset"> {
    pagination?: PaginationOptions;
}

// Interface for paginated results
export interface PaginatedResult<T> {
    data: T[];
    pagination: {
        page: number;
        limit: number;
        total: number;
        totalPages: number;
        hasNext: boolean;
        hasPrev: boolean;
    };
}

// Base model class with common static methods
export abstract class BaseModel<
    TModelAttributes extends {} = any,
    TCreationAttributes extends {} = TModelAttributes
> extends Model<TModelAttributes, TCreationAttributes> {
    /**
     * Find all records with optional filtering and pagination
     */
    static async findAllRecords<M extends Model>(
        this: ModelStatic<M>,
        options?: QueryOptions<Attributes<M>>
    ): Promise<M[]> {
        const { pagination, ...findOptions } = options || {};
        console.log('eda');
        if (pagination) {
            const { page = 1, limit = 10 } = pagination;
            const offset = (page - 1) * limit;

            return this.findAll({
                ...findOptions,
                limit,
                offset,
            });
        }

        return this.findAll(findOptions);
    }

    /**
     * Find all records with pagination and total count
     */
    static async findAllPaginated<M extends Model>(
        this: ModelStatic<M>,
        options?: QueryOptions<Attributes<M>>
    ): Promise<PaginatedResult<M>> {
        const { pagination, ...findOptions } = options || {};
        const { page = 1, limit = 10 } = pagination || {};
        const offset = (page - 1) * limit;

        const { count, rows } = await this.findAndCountAll({
            ...findOptions,
            limit,
            offset,
            distinct: true,
        });

        const totalPages = Math.ceil(count / limit);

        return {
            data: rows,
            pagination: {
                page,
                limit,
                total: count,
                totalPages,
                hasNext: page < totalPages,
                hasPrev: page > 1,
            },
        };
    }

    /**
     * Find a record by ID
     */
    static async findById<M extends Model>(
        this: ModelStatic<M>,
        id: number | string,
        options?: Omit<FindOptions<Attributes<M>>, "where">
    ): Promise<M | null> {
        return this.findByPk(id, options);
    }

    /**
     * Find a single record with conditions
     */
    static async findOneRecord<M extends Model>(
        this: ModelStatic<M>,
        options?: FindOptions<Attributes<M>>
    ): Promise<M | null> {
        return this.findOne(options);
    }

    /**
     * Create a new record
     */
    static async createRecord<M extends Model>(
        this: ModelStatic<M>,
        values: CreationAttributes<M>,
        options?: CreateOptions<Attributes<M>>
    ): Promise<M> {
        return this.create(values, options);
    }

    /**
     * Update a record by ID
     */
    static async updateById<M extends Model>(
        this: ModelStatic<M>,
        id: number | string,
        values: Partial<Attributes<M>>,
        options?: Omit<UpdateOptions<Attributes<M>>, "where">
    ): Promise<[number, M[]]> {
        const result = await this.update(values, {
            ...options,
            where: { id } as any,
            returning: true,
        });
        return result as [number, M[]];
    }

    /**
     * Delete a record by ID
     */
    static async deleteById<M extends Model>(
        this: ModelStatic<M>,
        id: number | string,
        options?: Omit<DestroyOptions<Attributes<M>>, "where">
    ): Promise<number> {
        return this.destroy({
            ...options,
            where: { id } as any,
        });
    }

    /**
     * Count records with optional conditions
     */
    static async countRecords<M extends Model>(
        this: ModelStatic<M>,
        options?: CountOptions<Attributes<M>>
    ): Promise<number> {
        return this.count(options);
    }

    /**
     * Check if a record exists by ID
     */
    static async existsById<M extends Model>(
        this: ModelStatic<M>,
        id: number | string
    ): Promise<boolean> {
        const count = await this.count({
            where: { id } as any,
        });
        return count > 0;
    }

    /**
     * Find or create a record
     */
    static async findOrCreateRecord<M extends Model>(
        this: ModelStatic<M>,
        options: {
            where: WhereOptions<Attributes<M>>;
            defaults?: CreationAttributes<M>;
        }
    ): Promise<[M, boolean]> {
        return this.findOrCreate(options);
    }

    /**
     * Bulk create records
     */
    static async bulkCreateRecords<M extends Model>(
        this: ModelStatic<M>,
        records: CreationAttributes<M>[],
        options?: CreateOptions<Attributes<M>>
    ): Promise<M[]> {
        return this.bulkCreate(records, options);
    }

    /**
     * Bulk update records
     */
    static async bulkUpdateRecords<M extends Model>(
        this: ModelStatic<M>,
        values: Partial<Attributes<M>>,
        options: Omit<UpdateOptions<Attributes<M>>, "returning"> & {
            returning?: boolean;
        }
    ): Promise<[number, M[]]> {
        const result = await this.update(values, {
            ...options,
            returning: options.returning ?? false,
        });
        return result as unknown as [number, M[]];
    }

    /**
     * Bulk delete records
     */
    static async bulkDeleteRecords<M extends Model>(
        this: ModelStatic<M>,
        options: DestroyOptions<Attributes<M>>
    ): Promise<number> {
        return this.destroy(options);
    }

    /**
     * Get the latest records
     */
    static async getLatest<M extends Model>(
        this: ModelStatic<M>,
        limit: number = 10,
        options?: Omit<FindOptions<Attributes<M>>, "limit" | "order">
    ): Promise<M[]> {
        return this.findAll({
            ...options,
            limit,
            order: [["createdAt", "DESC"]] as Order,
        });
    }

    /**
     * Search records by a field containing a value (case-insensitive)
     */
    static async search<M extends Model>(
        this: ModelStatic<M>,
        field: keyof Attributes<M>,
        value: string,
        options?: FindOptions<Attributes<M>>
    ): Promise<M[]> {
        return this.findAll({
            ...options,
            where: {
                ...options?.where,
                [field]: {
                    [Op.iLike]: `%${value}%`,
                },
            } as any,
        });
    }
}

export default BaseModel;
