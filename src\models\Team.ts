import { DataTypes, Optional } from "sequelize";
import sequelize from "../config/database";
import BaseModel from "./BaseModel";
import TeamUser from "./TeamUser";
import User from "./User";

interface TeamAttributes {
    id: number;
    name: string;
    description?: string;
    leaderId?: number; // User ID of the team leader
    isActive: boolean;
    createdAt?: Date;
    updatedAt?: Date;
}

interface TeamCreationAttributes
    extends Optional<
        TeamAttributes,
        | "id"
        | "description"
        | "leaderId"
        | "isActive"
        | "createdAt"
        | "updatedAt"
    > {}

class Team
    extends BaseModel<TeamAttributes, TeamCreationAttributes>
    implements TeamAttributes
{
    public id!: number;
    public name!: string;
    public description?: string;
    public leaderId?: number;
    public isActive!: boolean;
    public readonly createdAt!: Date;
    public readonly updatedAt!: Date;

    // Static method to get team by name
    static async findByName(name: string): Promise<Team | null> {
        return this.findOne({ where: { name } });
    }

    // Static method to get all active teams
    static async getActiveTeams(): Promise<Team[]> {
        return this.findAll({
            where: { isActive: true },
            order: [["name", "ASC"]],
        });
    }

    // Static method to get teams by leader
    static async getTeamsByLeader(leaderId: number): Promise<Team[]> {
        return this.findAll({
            where: { leaderId, isActive: true },
            order: [["name", "ASC"]],
        });
    }

    // Method to get team members count
    public async getMembersCount(): Promise<number> {
        return TeamUser.countRecords({
            where: { teamId: this.id },
        });
    }

    // Method to get team members
    public async getMembers(): Promise<any[]> {
        const teamUsers = await TeamUser.findAll({
            where: { teamId: this.id },
            include: [
                {
                    model: User,
                    attributes: [
                        "id",
                        "firstName",
                        "lastName",
                        "email",
                        "phone",
                    ],
                    as: 'User'
                },
            ],
            order: [["joinedAt", "ASC"]],
        });

        return teamUsers.map((tu: any) => ({
            ...tu.User.toJSON(),
            joinedAt: tu.joinedAt,
            role: tu.role,
        }));
    }

    // Method to check if user is team leader
    public isLeader(userId: number): boolean {
        return this.leaderId === userId;
    }

    // Method to check if user is team member
    public async hasMember(userId: number): Promise<boolean> {
        const teamUser = await TeamUser.findOne({
            where: { teamId: this.id, userId },
        });
        return !!teamUser;
    }

    // Method to activate/deactivate team
    public async setActive(isActive: boolean): Promise<void> {
        await this.update({ isActive });
    }

    // Method to check if team has future bookings
    public async hasFutureBookings(): Promise<{ count: number; hasBookings: boolean }> {
        const { Op } = require("sequelize");
        const Booking = require("./Booking").default;
        const now = new Date();

        const futureBookingsCount = await Booking.countRecords({
            where: {
                teamId: this.id,
                status: {
                    [Op.in]: ['pending', 'confirmed', 'in_progress']
                },
                startTime: {
                    [Op.gt]: now
                }
            }
        });

        return {
            count: futureBookingsCount,
            hasBookings: futureBookingsCount > 0
        };
    }

    // Method to check if team has bookings in progress
    public async hasInProgressBookings(): Promise<{ count: number; hasBookings: boolean }> {
        const Booking = require("./Booking").default;

        const inProgressBookingsCount = await Booking.countRecords({
            where: {
                teamId: this.id,
                status: 'in_progress'
            }
        });

        return {
            count: inProgressBookingsCount,
            hasBookings: inProgressBookingsCount > 0
        };
    }

    // Method to check if team can be safely deleted
    public async canBeDeleted(): Promise<{ canDelete: boolean; reasons: string[] }> {
        const reasons: string[] = [];

        const [memberCount, futureBookings, inProgressBookings] = await Promise.all([
            this.getMembersCount(),
            this.hasFutureBookings(),
            this.hasInProgressBookings()
        ]);

        // Check for members
        if (memberCount > 0) {
            reasons.push(`Team has ${memberCount} active member(s)`);
        }


        // Check for future bookings
        if (futureBookings.hasBookings) {
            reasons.push(`Team has ${futureBookings.count} future booking(s)`);
        }

        // Check for in-progress bookings
        if (inProgressBookings.hasBookings) {
            reasons.push(`Team has ${inProgressBookings.count} booking(s) currently in progress`);
        }

        return {
            canDelete: reasons.length === 0,
            reasons
        };
    }
}

Team.init(
    {
        id: {
            type: DataTypes.INTEGER,
            autoIncrement: true,
            primaryKey: true,
        },
        name: {
            type: DataTypes.STRING,
            allowNull: false,
            unique: true,
            validate: {
                notEmpty: true,
                len: [2, 100],
            },
        },
        description: {
            type: DataTypes.TEXT,
            allowNull: true,
        },
        leaderId: {
            type: DataTypes.INTEGER,
            allowNull: true,
            references: {
                model: "users",
                key: "id",
            },
            onDelete: "SET NULL",
            onUpdate: "CASCADE",
        },
        isActive: {
            type: DataTypes.BOOLEAN,
            allowNull: false,
            defaultValue: true,
        },
    },
    {
        sequelize,
        modelName: "Team",
        tableName: "teams",
        indexes: [
            {
                unique: true,
                fields: ["name"],
            },
            {
                fields: ["leaderId"],
            },
            {
                fields: ["isActive"],
            },
        ],
    }
);

export default Team;
