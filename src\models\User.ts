import { DataTypes, Optional } from "sequelize";
import sequelize from "../config/database";
import bcrypt from "bcrypt";
import BaseModel from "./BaseModel";
import stripeService from "../services/stripeService";

interface UserAttributes {
    id: number;
    firstName: string;
    lastName: string;
    email: string;
    password: string;
    phone: string;
    stripeCustomerId?: string;
    createdAt?: Date;
    updatedAt?: Date;
}

interface UserCreationAttributes
    extends Optional<UserAttributes, "id" | "stripeCustomerId" | "createdAt" | "updatedAt"> {}

class User
    extends BaseModel<UserAttributes, UserCreationAttributes>
    implements UserAttributes
{
    public id!: number;
    public firstName!: string;
    public lastName!: string;
    public email!: string;
    public password!: string;
    public phone!: string;
    public stripeCustomerId?: string;
    public readonly createdAt!: Date;
    public readonly updatedAt!: Date;

    // Method to validate password
    public async validatePassword(password: string): Promise<boolean> {
        return bcrypt.compare(password, this.password);
    }

    // Method to get user's roles
    public async getRoles(): Promise<string[]> {
        const { Role, UserRole } = require("./index");
        const userRoles = await UserRole.findAll({
            where: { userId: this.id },
            include: [{ model: Role, attributes: ["name"], as: 'Role' }],
        });
        return userRoles.map((ur: any) => ur.Role.name);
    }

    // Method to check if user has a specific role
    public async hasRole(roleName: string): Promise<boolean> {
        const roles = await this.getRoles();
        return roles.includes(roleName);
    }

    // Method to check if user has any of the specified roles
    public async hasAnyRole(roleNames: string[]): Promise<boolean> {
        const userRoles = await this.getRoles();
        return roleNames.some((role) => userRoles.includes(role));
    }

    // Method to assign role to user
    public async assignRole(
        roleName: string,
        assignedBy?: number
    ): Promise<void> {
        const { Role, UserRole } = require("./index");
        const role = await Role.findByName(roleName);
        if (!role) {
            throw new Error(`Role '${roleName}' not found`);
        }

        const existingUserRole = await UserRole.findOne({
            where: { userId: this.id, roleId: role.id },
        });

        if (!existingUserRole) {
            await UserRole.assignRoleToUser(this.id, role.id, assignedBy);
        }
    }

    // Method to remove role from user
    public async removeRole(roleName: string): Promise<void> {
        const { Role, UserRole } = require("./index");
        const role = await Role.findByName(roleName);
        if (!role) {
            throw new Error(`Role '${roleName}' not found`);
        }

        await UserRole.removeRoleFromUser(this.id, role.id);
    }

    // Team-related methods

    // Method to get user's teams
    public async getTeams(): Promise<any[]> {
        const { Team, TeamUser } = require('./index');
        const teamUsers = await TeamUser.findAll({
            where: { userId: this.id },
            include: [{
                model: Team,
                attributes: ['id', 'name', 'description', 'isActive']
            }],
            order: [['joinedAt', 'DESC']]
        });

        return teamUsers.map((tu: any) => ({
            ...tu.Team.toJSON(),
            role: tu.role,
            joinedAt: tu.joinedAt
        }));
    }

    // Method to check if user is in a specific team
    public async isInTeam(teamId: number): Promise<boolean> {
        const { TeamUser } = require('./index');
        const teamUser = await TeamUser.findOne({
            where: { userId: this.id, teamId }
        });
        return !!teamUser;
    }

    // Method to check if user is in any team with specific name
    public async isInTeamByName(teamName: string): Promise<boolean> {
        const { Team } = require('./index');
        const team = await Team.findByName(teamName);
        if (!team) {
            return false;
        }
        return this.isInTeam(team.id);
    }

    // Method to join team
    public async joinTeam(teamId: number, role: string = 'member', addedBy?: number): Promise<void> {
        const { Team, TeamUser } = require('./index');

        // Check if team exists
        const team = await Team.findById(teamId);
        if (!team) {
            throw new Error(`Team with ID ${teamId} not found`);
        }

        // Check if user is already in team
        const isAlreadyMember = await this.isInTeam(teamId);
        if (isAlreadyMember) {
            throw new Error(`User is already a member of team '${team.name}'`);
        }

        await TeamUser.addUserToTeam(this.id, teamId, role, addedBy);
    }

    // Method to leave team
    public async leaveTeam(teamId: number): Promise<void> {
        const { TeamUser } = require('./index');
        const removedCount = await TeamUser.removeUserFromTeam(this.id, teamId);

        if (removedCount === 0) {
            throw new Error(`User is not a member of this team`);
        }
    }

    // Method to get user's role in a specific team
    public async getTeamRole(teamId: number): Promise<string | null> {
        const { TeamUser } = require('./index');
        const teamUser = await TeamUser.findOne({
            where: { userId: this.id, teamId }
        });

        return teamUser ? teamUser.role : null;
    }

    // Method to check if user leads any teams
    public async getLeadingTeams(): Promise<any[]> {
        const { Team } = require('./index');
        return Team.findAll({
            where: { leaderId: this.id, isActive: true },
            order: [['name', 'ASC']]
        });
    }

    // Method to check if user is team leader
    public async isTeamLeader(teamId?: number): Promise<boolean> {
        const { Team } = require('./index');

        if (teamId) {
            const team = await Team.findById(teamId);
            return team ? team.leaderId === this.id : false;
        }

        // Check if user leads any team
        const leadingTeams = await this.getLeadingTeams();
        return leadingTeams.length > 0;
    }

    // Stripe-related methods

    // Method to get Stripe customer
    public async getStripeCustomer(): Promise<any> {
        if (!this.stripeCustomerId) {
            throw new Error('User does not have a Stripe customer ID');
        }

        try {
            return await stripeService.getCustomer(this.stripeCustomerId);
        } catch (error) {
            console.error(`Failed to get Stripe customer for user ${this.id}:`, error);
            throw error;
        }
    }

    // Method to create Stripe customer if not exists
    public async ensureStripeCustomer(): Promise<string> {
        if (this.stripeCustomerId) {
            return this.stripeCustomerId;
        }

        try {
            const fullName = `${this.firstName} ${this.lastName}`;
            const stripeCustomer = await stripeService.createCustomer(
                this.email,
                fullName,
                this.phone,
                {
                    userId: this.id.toString(),
                    createdAt: this.createdAt.toISOString(),
                }
            );

            // Update user with Stripe customer ID
            await this.update({ stripeCustomerId: stripeCustomer.id });

            console.log(`Stripe customer created for user ${this.id}: ${stripeCustomer.id}`);
            return stripeCustomer.id;
        } catch (error) {
            console.error(`Failed to create Stripe customer for user ${this.id}:`, error);
            throw error;
        }
    }

    // Method to get payment methods
    public async getPaymentMethods(): Promise<any[]> {
        if (!this.stripeCustomerId) {
            throw new Error('User does not have a Stripe customer ID');
        }

        try {
            return await stripeService.listPaymentMethods(this.stripeCustomerId);
        } catch (error) {
            console.error(`Failed to get payment methods for user ${this.id}:`, error);
            throw error;
        }
    }
}

User.init(
    {
        id: {
            type: DataTypes.INTEGER,
            autoIncrement: true,
            primaryKey: true,
        },
        firstName: {
            type: DataTypes.STRING,
            allowNull: false,
        },
        lastName: {
            type: DataTypes.STRING,
            allowNull: false,
        },
        email: {
            type: DataTypes.STRING,
            allowNull: false,
            unique: true,
            validate: {
                isEmail: true,
            },
        },
        password: {
            type: DataTypes.STRING,
            allowNull: false,
        },
        phone: {
            type: DataTypes.STRING,
            allowNull: false,
        },
        stripeCustomerId: {
            type: DataTypes.STRING,
            allowNull: true,
            unique: true,
        },
    },
    {
        sequelize,
        modelName: "User",
        tableName: "users",
        hooks: {
            beforeCreate: async (user: User) => {
                if (user.password) {
                    const salt = await bcrypt.genSalt(10);
                    user.password = await bcrypt.hash(user.password, salt);
                }
            },
            afterCreate: async (user: User) => {
                try {
                    // Create Stripe customer after user is successfully created
                    const fullName = `${user.firstName} ${user.lastName}`;
                    const stripeCustomer = await stripeService.createCustomer(
                        user.email,
                        fullName,
                        user.phone,
                        {
                            userId: user.id.toString(),
                            createdAt: user.createdAt.toISOString(),
                        }
                    );

                    // Update user with Stripe customer ID
                    await user.update({ stripeCustomerId: stripeCustomer.id });

                    console.log(`User ${user.id} linked to Stripe customer ${stripeCustomer.id}`);
                } catch (error) {
                    console.error(`Failed to create Stripe customer for user ${user.id}:`, error);
                    // Note: We don't throw here to avoid breaking user creation
                    // The Stripe customer can be created later if needed
                }
            },
            beforeUpdate: async (user: User) => {
                if (user.changed("password")) {
                    const salt = await bcrypt.genSalt(10);
                    user.password = await bcrypt.hash(user.password, salt);
                }
            },
        },
    }
);

export default User;
