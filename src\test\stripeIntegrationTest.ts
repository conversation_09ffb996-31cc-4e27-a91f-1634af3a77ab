/**
 * Test file for Stripe integration
 * Run this with: npx ts-node src/test/stripeIntegrationTest.ts
 * 
 * Make sure to set up your .env file with Stripe test keys before running
 */

import { User } from "../models";
import sequelize from "../config/database";
import stripeService from "../services/stripeService";

async function testStripeIntegration() {
    try {
        console.log("🧪 Testing Stripe Integration...\n");

        // Test database connection
        await sequelize.authenticate();
        console.log("✅ Database connection established");

        // Test Stripe service initialization
        console.log("✅ Stripe service initialized");

        // Test creating a user (should automatically create Stripe customer)
        console.log("\n📝 Testing user creation with Stripe customer...");
        
        const testUser = await User.createRecord({
            firstName: "John",
            lastName: "Doe",
            email: `test.stripe.${Date.now()}@example.com`,
            password: "testpassword123",
            phone: "+1234567890"
        });

        console.log(`✅ User created with ID: ${testUser.id}`);
        
        // Wait a moment for the afterCreate hook to complete
        await new Promise(resolve => setTimeout(resolve, 2000));
        
        // Reload user to get updated stripeCustomerId
        await testUser.reload();
        
        if (testUser.stripeCustomerId) {
            console.log(`✅ Stripe customer created: ${testUser.stripeCustomerId}`);
            
            // Test getting Stripe customer
            const stripeCustomer = await testUser.getStripeCustomer();
            console.log(`✅ Retrieved Stripe customer: ${stripeCustomer.email}`);
            
            // Test getting payment methods (should be empty for new customer)
            const paymentMethods = await testUser.getPaymentMethods();
            console.log(`✅ Payment methods retrieved: ${paymentMethods.length} methods`);
            
            // Test creating payment intent
            const paymentIntent = await stripeService.createPaymentIntent(
                2000, // $20.00
                'usd',
                testUser.stripeCustomerId,
                { testBooking: 'true' }
            );
            console.log(`✅ Payment intent created: ${paymentIntent.id}`);
            
        } else {
            console.log("⚠️  Stripe customer ID not found - check your Stripe configuration");
        }

        // Test ensureStripeCustomer method
        console.log("\n📝 Testing ensureStripeCustomer method...");
        const testUser2 = await User.createRecord({
            firstName: "Jane",
            lastName: "Smith",
            email: `test.stripe2.${Date.now()}@example.com`,
            password: "testpassword123",
            phone: "+1234567891"
        });

        // Manually clear the stripeCustomerId to test ensureStripeCustomer
        await testUser2.update({ stripeCustomerId: null });
        
        const customerId = await testUser2.ensureStripeCustomer();
        console.log(`✅ ensureStripeCustomer created: ${customerId}`);

        console.log("\n🎉 All Stripe integration tests passed!");
        
        // Cleanup test users
        console.log("\n🧹 Cleaning up test data...");
        if (testUser.stripeCustomerId) {
            await stripeService.deleteCustomer(testUser.stripeCustomerId);
        }
        if (testUser2.stripeCustomerId) {
            await stripeService.deleteCustomer(testUser2.stripeCustomerId);
        }
        await testUser.destroy();
        await testUser2.destroy();
        console.log("✅ Test data cleaned up");

    } catch (error) {
        console.error("❌ Stripe integration test failed:", error);
        
        if (error instanceof Error) {
            if (error.message.includes('STRIPE_SECRET_KEY')) {
                console.log("\n💡 Make sure to set up your .env file with Stripe test keys:");
                console.log("   STRIPE_SECRET_KEY=sk_test_...");
                console.log("   STRIPE_PUBLISHABLE_KEY=pk_test_...");
            }
        }
    } finally {
        await sequelize.close();
        console.log("\n🔌 Database connection closed");
    }
}

// Run the test if this file is executed directly
if (require.main === module) {
    testStripeIntegration();
}

export { testStripeIntegration };
