import { DataTypes, Optional } from "sequelize";
import sequelize from "../config/database";
import BaseModel from "./BaseModel";

interface RoleAttributes {
    id: number;
    name: string;
    description?: string;
    createdAt?: Date;
    updatedAt?: Date;
}

interface RoleCreationAttributes
    extends Optional<
        RoleAttributes,
        "id" | "description" | "createdAt" | "updatedAt"
    > {}

class Role
    extends BaseModel<RoleAttributes, RoleCreationAttributes>
    implements RoleAttributes
{
    public id!: number;
    public name!: string;
    public description?: string;
    public readonly createdAt!: Date;
    public readonly updatedAt!: Date;

    // Static method to get role by name
    static async findByName(name: string): Promise<Role | null> {
        return this.findOne({ where: { name } });
    }

    // Static method to get all role names
    static async getAllRoleNames(): Promise<string[]> {
        const roles = await this.findAll({ attributes: ["name"] });
        return roles.map((role) => role.name);
    }
}

Role.init(
    {
        id: {
            type: DataTypes.INTEGER,
            autoIncrement: true,
            primaryKey: true,
        },
        name: {
            type: DataTypes.STRING,
            allowNull: false,
            unique: true,
            validate: {
                notEmpty: true,
                len: [2, 50],
            },
        },
        description: {
            type: DataTypes.TEXT,
            allowNull: true,
        },
    },
    {
        sequelize,
        modelName: "Role",
        tableName: "roles",
        indexes: [
            {
                unique: true,
                fields: ["name"],
            },
        ],
    }
);

export default Role;
