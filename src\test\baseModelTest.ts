/**
 * Simple test to verify BaseModel functionality
 * Run this with: npx ts-node src/test/baseModelTest.ts
 */

import { User, Service, Booking } from "../models";
import sequelize from "../config/database";

async function testBaseModel() {
    try {
        // Test database connection
        await sequelize.authenticate();
        console.log("✅ Database connection established");

        // Test User model methods (inherited from BaseModel)
        console.log("\n🧪 Testing User model methods:");
        
        // Test findAll with pagination
        const users = await User.findAllRecords({
            pagination: { page: 1, limit: 5 }
        });
        console.log(`✅ findAll with pagination: Found ${users.length} users`);

        // Test findAllPaginated
        const paginatedUsers = await User.findAllPaginated({
            pagination: { page: 1, limit: 3 }
        });
        console.log(`✅ findAllPaginated: Found ${paginatedUsers.data.length} users, total: ${paginatedUsers.pagination.total}`);

        // Test count
        const userCount = await User.countRecords();
        console.log(`✅ countRecords: Total users: ${userCount}`);

        // Test Service model methods
        console.log("\n🧪 Testing Service model methods:");
        
        const services = await Service.findAllRecords();
        console.log(`✅ Service.findAll: Found ${services.length} services`);

        const serviceCount = await Service.countRecords();
        console.log(`✅ Service.countRecords: Total services: ${serviceCount}`);

        // Test Booking model methods
        console.log("\n🧪 Testing Booking model methods:");
        
        const bookings = await Booking.getLatest(3);
        console.log(`✅ Booking.getLatest: Found ${bookings.length} latest bookings`);

        // Test exists method
        if (users.length > 0) {
            const exists = await User.existsById(users[0].id);
            console.log(`✅ User.existsById: User ${users[0].id} exists: ${exists}`);
        }

        // Test search method (if users exist)
        if (users.length > 0) {
            const searchResults = await User.search('firstName', users[0].firstName.substring(0, 2));
            console.log(`✅ User.search: Found ${searchResults.length} users matching search`);
        }

        console.log("\n🎉 All BaseModel tests passed!");

    } catch (error) {
        console.error("❌ Test failed:", error);
    } finally {
        await sequelize.close();
        console.log("🔌 Database connection closed");
    }
}

// Run the test
if (require.main === module) {
    testBaseModel();
}

export default testBaseModel;
